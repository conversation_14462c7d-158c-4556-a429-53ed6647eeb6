/**
 * Theme Configuration
 * Basic theme constants for the Vierla application
 */

// Vierla Brand Colors - New Color Palette
export const colors = {
  // Primary brand colors - New Vierla Palette
  primary: '#364035', // <PERSON> Green - Primary brand color for main CTAs and navigation
  secondary: '#8B9A8C', // Sage Green - Secondary color for supporting elements
  primaryLight: '#4A5A4B', // Lighter Forest Green variant
  primaryDark: '#2A302B', // Darker Forest Green variant

  // Background colors
  background: {
    primary: '#FFFFFF', // Pure White - Primary background color (updated for white theme)
    secondary: '#F4F1E8', // Warm Cream - Secondary background color
    tertiary: '#C9BEB0', // Soft Taupe - Tertiary background color
    light: '#F9FAFB', // Keep for compatibility
    dark: '#121212', // Deep Space (for dark mode)
  },

  // Surface colors
  surface: {
    light: '#FFFFFF', // Pure White
    dark: '#1E1E1E', // Charcoal (for dark mode)
  },

  // Text colors - WCAG AA compliant
  text: {
    primary: '#2D2A26', // Deep Charcoal - Primary text color (7.8:1 contrast on Warm Cream)
    secondary: '#5A5A5A', // WCAG AA compliant gray - Secondary text (4.6:1 contrast)
    tertiary: '#666666', // WCAG AA compliant gray - Tertiary text (5.7:1 contrast)
    onDark: '#E0E0E0', // Silver (for dark mode)
    sage: '#8B9A8C', // Original Sage Green - for decorative use only (not for text)
  },

  // Utility colors
  white: '#FFFFFF',
  black: '#000000',

  // Status colors
  success: '#2E7D32', // Forest Green
  warning: '#F59E0B', // Keep existing
  error: '#C62828', // Crimson Red
  info: '#3B82F6', // Keep existing

  // Additional colors - WCAG AA compliant
  accent: '#A67C52', // Darker Rich Gold variant - Accent color (3.2:1 contrast on Warm Cream)
  accentOriginal: '#B8956A', // Original Rich Gold - for decorative use only
  taupe: '#C9BEB0', // Soft Taupe (Legacy)
  border: '#C4CEC1', // Updated border color from extended range

  // Extended Color Range - New Vierla Palette
  extended: {
    darkest: '#171c17', // Darkest green for dark mode backgrounds
    dark: '#2d342d', // Dark green variant
    mediumDark: '#3d493c', // Medium-dark green
    medium: '#495a47', // Medium green
    mediumLight: '#5e715b', // Medium-light green
    light: '#798c75', // Light green
    sageVariant: '#9dad9b', // Sage Green variant
    veryLight: '#c4cec1', // Very light green
    nearWhite: '#e2e7e0', // Near-white green tint
    lightest: '#f6f7f6', // Lightest green tint
  },
};

// Spacing system
export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  '2xl': 48,
  '3xl': 64,
};

// Typography system based on UI/UX design document
// Modular scale with 1.250 ratio (Major Third) and 16pt base size
export const typography = {
  // Font families - following design system specifications
  fontFamily: {
    heading: 'Lora', // Serif for headings - elegant and sophisticated
    body: 'Inter',   // Sans-serif for body and UI text - optimal legibility
    system: 'System', // Fallback system font
  },

  // Font weights
  fontWeight: {
    regular: '400',
    semiBold: '600',
    bold: '700',
  },

  // Typography scale (16pt base with 1.250 ratio) - exact sizes from design doc
  fontSize: {
    display: 40,    // Major screen titles (e.g., "Find Your Sanctuary")
    headline1: 32,  // Section headers (e.g., "Featured Professionals")
    headline2: 24,  // Card titles, smaller section headers
    headline3: 20,  // Sub-card titles, modal titles
    body: 16,       // Primary paragraph text, descriptions
    subtitle: 14,   // Captions, secondary info text
    button: 16,     // Button labels
    caption: 12,    // Smallest text, legal notes, timestamps
  },

  // Line heights (optimized for readability) - exact values from design doc
  lineHeight: {
    display: 48,    // 40pt * 1.2
    headline1: 40,  // 32pt * 1.25
    headline2: 32,  // 24pt * 1.33
    headline3: 28,  // 20pt * 1.4
    body: 24,       // 16pt * 1.5 (150% for optimal readability)
    subtitle: 20,   // 14pt * 1.43
    button: 16,     // 16pt * 1.0 (tight for buttons)
    caption: 16,    // 12pt * 1.33
  },

  // Letter spacing (for fine-tuning readability) - exact values from design doc
  letterSpacing: {
    display: -0.2,   // -0.5% of font size
    headline1: 0,
    headline2: 0,
    headline3: 0.1,  // +0.5% of font size
    body: 0,
    subtitle: 0.07,  // +0.5% of font size
    button: 0.16,    // +1.0% of font size
    caption: 0.06,   // +0.5% of font size
  },
};

// Border radius - following design system specifications
export const borderRadius = {
  sm: 4,
  md: 8,       // Primary button corner radius
  lg: 12,      // Primary card radius from design system
  xl: 16,
  pill: 9999,  // For search bars and pill-shaped elements
};

// Shadows - following design system elevation levels
export const shadows = {
  // Level 1 (Low) - for cards, gentle lift off background
  level1: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.08,  // Subtle shadow for cards
    shadowRadius: 3,
    elevation: 2,
  },
  // Level 2 (Medium) - for modals and floating elements
  level2: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.12,
    shadowRadius: 6,
    elevation: 4,
  },
  // Level 3 (High) - for important floating elements
  level3: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.16,
    shadowRadius: 12,
    elevation: 8,
  },
  // Legacy shadows for backward compatibility
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 3,
    elevation: 2,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.12,
    shadowRadius: 6,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.16,
    shadowRadius: 12,
    elevation: 8,
  },
};

// Export default theme object
export const theme = {
  colors,
  spacing,
  typography,
  borderRadius,
  shadows,
};

export default theme;
