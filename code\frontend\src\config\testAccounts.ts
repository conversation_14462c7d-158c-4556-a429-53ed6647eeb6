/**
 * Test Accounts Configuration
 * Consolidated test accounts for development and testing
 * Part of EPIC-05-CRITICAL: Authentication & UI Enhancement
 */

export interface TestAccount {
  email: string;
  password: string;
  role: 'customer' | 'service_provider' | 'admin';
  name: string;
  description: string;
  features: string[];
}

/**
 * Consolidated test accounts for different user roles and scenarios
 * These accounts should be available in development and testing environments
 */
export const TEST_ACCOUNTS: Record<string, TestAccount> = {
  // Customer accounts (matching backend test accounts)
  customer_basic: {
    email: '<EMAIL>',
    password: 'VierlaTest123!',
    role: 'customer',
    name: '<PERSON>',
    description: 'Basic customer account for testing customer features',
    features: [
      'Browse services',
      'Book appointments',
      'View booking history',
      'Rate and review services',
      'Manage profile',
    ],
  },

  customer_premium: {
    email: '<EMAIL>',
    password: 'VierlaTest123!',
    role: 'customer',
    name: '<PERSON>',
    description: 'Premium customer account with advanced features',
    features: [
      'All basic customer features',
      'Priority booking',
      'Exclusive services',
      'Advanced analytics',
      'Premium support',
    ],
  },

  customer_new: {
    email: '<EMAIL>',
    password: 'VierlaTest123!',
    role: 'customer',
    name: '<PERSON>',
    description: 'Regular customer account for testing standard features',
    features: [
      'Standard customer features',
      'Regular booking access',
      'Basic profile management',
      'Service browsing',
    ],
  },

  // Service provider accounts (matching backend test accounts)
  provider_individual: {
    email: '<EMAIL>',
    password: 'VierlaTest123!',
    role: 'service_provider',
    name: 'Trendy Cuts Salon',
    description: 'Professional salon service provider',
    features: [
      'Manage services',
      'Accept/decline bookings',
      'Set availability',
      'View earnings',
      'Customer communication',
    ],
  },

  provider_business: {
    email: '<EMAIL>',
    password: 'VierlaTest123!',
    role: 'service_provider',
    name: 'Elite Cuts Barbershop',
    description: 'Professional barbershop with multiple services',
    features: [
      'All individual provider features',
      'Multiple service offerings',
      'Team management',
      'Advanced analytics',
      'Business reporting',
    ],
  },

  provider_verified: {
    email: '<EMAIL>',
    password: 'VierlaTest123!',
    role: 'service_provider',
    name: 'Luxe Nail Lounge',
    description: 'Verified nail salon with high ratings',
    features: [
      'All provider features',
      'Verified badge',
      'Premium listing',
      'Priority in search',
      'Enhanced profile',
    ],
  },

  // Admin accounts (matching backend test accounts)
  admin_super: {
    email: '<EMAIL>',
    password: 'VierlaAdmin123!',
    role: 'admin',
    name: 'System Administrator',
    description: 'Super admin account with full system access',
    features: [
      'Full system access',
      'User management',
      'Service management',
      'Analytics and reporting',
      'System configuration',
      'Content management',
    ],
  },

  admin_support: {
    email: '<EMAIL>',
    password: 'VierlaSupport123!',
    role: 'admin',
    name: 'Customer Support',
    description: 'Support admin for customer service operations',
    features: [
      'Customer support tools',
      'Ticket management',
      'User assistance',
      'Basic reporting',
      'Content moderation',
    ],
  },

  admin_moderator: {
    email: '<EMAIL>',
    password: 'VierlaTest123!',
    role: 'customer',
    name: 'Priya Patel',
    description: 'New customer account for testing onboarding flow',
    features: [
      'First-time user experience',
      'Onboarding tutorials',
      'Welcome offers',
      'Basic customer features',
    ],
  },
};

/**
 * Get test accounts by role
 */
export const getTestAccountsByRole = (role: 'customer' | 'service_provider' | 'admin'): TestAccount[] => {
  return Object.values(TEST_ACCOUNTS).filter(account => account.role === role);
};

/**
 * Get all test account emails for quick reference
 */
export const getAllTestAccountEmails = (): string[] => {
  return Object.values(TEST_ACCOUNTS).map(account => account.email);
};

/**
 * Get test account by email
 */
export const getTestAccountByEmail = (email: string): TestAccount | undefined => {
  return Object.values(TEST_ACCOUNTS).find(account => account.email === email);
};

/**
 * Check if an email is a test account
 */
export const isTestAccount = (email: string): boolean => {
  return getAllTestAccountEmails().includes(email);
};

/**
 * Get default test account for each role (for quick testing)
 */
export const DEFAULT_TEST_ACCOUNTS = {
  customer: TEST_ACCOUNTS.customer_basic,
  service_provider: TEST_ACCOUNTS.provider_individual,
  admin: TEST_ACCOUNTS.admin_super,
} as const;

/**
 * Test account credentials for easy copy-paste during development
 */
export const TEST_CREDENTIALS = {
  // Customer accounts
  'Emma Thompson': { email: '<EMAIL>', password: 'VierlaTest123!' },
  'Sarah Johnson': { email: '<EMAIL>', password: 'VierlaTest123!' },
  'Michael Chen': { email: '<EMAIL>', password: 'VierlaTest123!' },
  'Priya Patel': { email: '<EMAIL>', password: 'VierlaTest123!' },

  // Provider accounts
  'Trendy Cuts Salon': { email: '<EMAIL>', password: 'VierlaTest123!' },
  'Elite Cuts Barbershop': { email: '<EMAIL>', password: 'VierlaTest123!' },
  'Luxe Nail Lounge': { email: '<EMAIL>', password: 'VierlaTest123!' },

  // Admin accounts
  'System Administrator': { email: '<EMAIL>', password: 'VierlaAdmin123!' },
  'Customer Support': { email: '<EMAIL>', password: 'VierlaSupport123!' },
} as const;

/**
 * Development helper: Log all test accounts to console
 */
export const logTestAccounts = (): void => {
  if (__DEV__) {
    console.log('=== Available Test Accounts ===');
    Object.entries(TEST_CREDENTIALS).forEach(([name, credentials]) => {
      console.log(`${name}: ${credentials.email} / ${credentials.password}`);
    });
    console.log('===============================');
  }
};

/**
 * Development helper: Get quick login function for test accounts
 */
export const getQuickLoginFunction = (accountKey: keyof typeof TEST_ACCOUNTS) => {
  const account = TEST_ACCOUNTS[accountKey];
  if (!account) {
    throw new Error(`Test account '${accountKey}' not found`);
  }
  
  return {
    email: account.email,
    password: account.password,
    role: account.role,
    name: account.name,
  };
};

export default TEST_ACCOUNTS;
