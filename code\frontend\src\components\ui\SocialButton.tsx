/**
 * SocialButton Component
 * Atomic design component for social authentication buttons (Google, Apple)
 * Following the "digital sanctuary" design philosophy with Forest Green theme
 */

import React from 'react';
import {
  TouchableOpacity,
  View,
  StyleSheet,
  ActivityIndicator,
  ViewStyle,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Text } from '../Text';
import { useTheme } from '../../contexts/ThemeContext';

export interface SocialButtonProps {
  provider: 'google' | 'apple';
  onPress: () => void;
  loading?: boolean;
  disabled?: boolean;
  style?: ViewStyle;
  testID?: string;
}

export const SocialButton: React.FC<SocialButtonProps> = ({
  provider,
  onPress,
  loading = false,
  disabled = false,
  style,
  testID,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  const getProviderConfig = () => {
    switch (provider) {
      case 'google':
        return {
          iconName: 'logo-google' as keyof typeof Ionicons.glyphMap,
          text: 'Continue with Google',
          backgroundColor: '#FFFFFF',
          textColor: '#1F1F1F',
          borderColor: '#DADCE0',
        };
      case 'apple':
        return {
          iconName: 'logo-apple' as keyof typeof Ionicons.glyphMap,
          text: 'Continue with Apple',
          backgroundColor: '#000000',
          textColor: '#FFFFFF',
          borderColor: '#000000',
        };
      default:
        return {
          iconName: 'log-in-outline' as keyof typeof Ionicons.glyphMap,
          text: 'Continue',
          backgroundColor: colors.background.primary,
          textColor: colors.text.primary,
          borderColor: colors.border.primary,
        };
    }
  };

  const config = getProviderConfig();

  const buttonStyle = [
    styles.button,
    {
      backgroundColor: config.backgroundColor,
      borderColor: config.borderColor,
    },
    disabled && styles.disabled,
    style,
  ];

  const textStyle = [
    styles.text,
    { color: config.textColor },
    disabled && styles.disabledText,
  ];

  return (
    <TouchableOpacity
      style={buttonStyle}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.7}
      testID={testID}
      accessibilityLabel={`${config.text} button`}
      accessibilityRole="button"
      accessibilityState={{ disabled: disabled || loading }}
    >
      <View style={styles.content}>
        {loading ? (
          <ActivityIndicator
            size="small"
            color={config.textColor}
            style={styles.loader}
          />
        ) : (
          <Ionicons
            name={config.iconName}
            size={18}
            color={config.textColor}
            style={styles.icon}
          />
        )}
        <Text style={textStyle}>{config.text}</Text>
      </View>
    </TouchableOpacity>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    button: {
      height: 48,
      borderRadius: 8,
      borderWidth: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 16,
      // Subtle shadow following design system Level 1 (Low)
      shadowColor: colors.text.primary,
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 1,
    },
    content: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    icon: {
      marginRight: 12,
    },
    text: {
      fontSize: 16,
      fontWeight: '600',
      fontFamily: 'Inter', // Following design system typography
    },
    loader: {
      marginRight: 12,
    },
    disabled: {
      opacity: 0.5,
    },
    disabledText: {
      opacity: 0.5,
    },
  });
