/**
 * Main Navigator
 * Handles role-based navigation for authenticated users
 */

import React from 'react';
import { View } from 'react-native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';

import { HomeScreen } from '../screens/main/HomeScreen';
import { ServicesScreen } from '../screens/main/ServicesScreen';
import { BookingsScreen } from '../screens/main/BookingsScreen';
import { ProfileScreen, SettingsScreen } from '../screens/main';
import { ProviderNavigator } from './ProviderNavigator';
import { useAuth } from '../contexts/AuthContext';
import { LoadingScreen } from '../screens/LoadingScreen';
import { TabBarIcon } from '../components/navigation/TabBarIcon';

export type MainTabParamList = {
  Home: undefined;
  Services: undefined;
  Bookings: undefined;
  ProfileStack: undefined;
};

export type ProfileStackParamList = {
  Profile: undefined;
  Settings: undefined;
};

const Tab = createBottomTabNavigator<MainTabParamList>();
const ProfileStack = createStackNavigator<ProfileStackParamList>();

const ProfileStackNavigator: React.FC = () => {
  return (
    <ProfileStack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      <ProfileStack.Screen name="Profile" component={ProfileScreen} />
      <ProfileStack.Screen name="Settings" component={SettingsScreen} />
    </ProfileStack.Navigator>
  );
};

const CustomerNavigator: React.FC = () => {
  const { colors } = useTheme();

  return (
    <Tab.Navigator
      testID="main-tab-navigator"
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;
          let showBadge = false;
          let badgeCount = 0;

          if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Services') {
            iconName = focused ? 'grid' : 'grid-outline';
          } else if (route.name === 'Bookings') {
            iconName = focused ? 'calendar' : 'calendar-outline';
            // Example: Show badge for pending bookings
            showBadge = true;
            badgeCount = 2; // This would come from your booking state
          } else if (route.name === 'ProfileStack') {
            iconName = focused ? 'person' : 'person-outline';
            // Example: Show dot for profile updates or verification status
            showBadge = true; // This would be conditional based on profile status
          } else {
            iconName = 'help-outline';
          }

          return (
            <TabBarIcon
              name={iconName}
              focused={focused}
              size={size}
              color={color}
              showBadge={showBadge}
              badgeCount={badgeCount}
              testID={`tab-icon-${route.name.toLowerCase()}`}
            />
          );
        },
        tabBarActiveTintColor: colors.primary, // Forest Green (#364035) for active
        tabBarInactiveTintColor: colors.secondary, // Sage Green (#8B9A8C) for inactive
        tabBarStyle: {
          backgroundColor: colors.background.secondary, // Warm Cream background
          borderTopColor: colors.extended.sageVariant, // Subtle border
          borderTopWidth: 1,
          paddingTop: 8,
          paddingBottom: 8,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
          marginTop: 4,
        },
        headerShown: false,
      })}
    >
      <Tab.Screen name="Home" component={HomeScreen} />
      <Tab.Screen name="Services" component={ServicesScreen} />
      <Tab.Screen name="Bookings" component={BookingsScreen} />
      <Tab.Screen
        name="ProfileStack"
        component={ProfileStackNavigator}
        options={{ title: 'Profile' }}
      />
    </Tab.Navigator>
  );
};

export const MainNavigator: React.FC = () => {
  const { isProvider, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingScreen />;
  }

  // Return appropriate navigator based on user role
  if (isProvider) {
    return <ProviderNavigator />;
  }

  // Default to customer navigator
  return (
    <View testID="main-navigator" style={{ flex: 1 }}>
      <CustomerNavigator />
    </View>
  );
};
