/**
 * Profile API Service
 * Handles all profile-related API calls
 */

import { apiClient } from './client';

// Types for API requests and responses
export interface User {
  id: string;
  email: string;
  username: string;
  first_name: string;
  last_name: string;
  full_name: string;
  phone?: string;
  role: 'customer' | 'service_provider' | 'admin';
  avatar?: string;
  date_of_birth?: string;
  bio?: string;
  account_status: string;
  is_verified: boolean;
  email_verified_at?: string;
  phone_verified_at?: string;
  created_at: string;
  updated_at: string;
}

export interface UserProfile {
  // Location Information
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  country?: string;
  latitude?: number;
  longitude?: number;
  full_address?: string; // computed
  has_location?: boolean; // computed

  // Business Information (for providers)
  business_name?: string;
  business_description?: string;
  years_of_experience?: number;
  website?: string;
  instagram?: string;
  facebook?: string;

  // Preferences
  search_radius?: number;
  auto_accept_bookings?: boolean;
  show_phone_publicly?: boolean;
  show_email_publicly?: boolean;
  allow_reviews?: boolean;

  // Timestamps
  created_at?: string;
  updated_at?: string;
}

export interface ProfileUpdateRequest {
  first_name?: string;
  last_name?: string;
  phone?: string;
  bio?: string;
  date_of_birth?: string;
}

export interface ProfileDetailsUpdateRequest {
  // Location Information
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  country?: string;
  latitude?: number;
  longitude?: number;

  // Business Information (for providers)
  business_name?: string;
  business_description?: string;
  years_of_experience?: number;
  website?: string;
  instagram?: string;
  facebook?: string;

  // Preferences
  search_radius?: number;
  auto_accept_bookings?: boolean;
  show_phone_publicly?: boolean;
  show_email_publicly?: boolean;
  allow_reviews?: boolean;
}

export interface AvatarUploadResponse {
  avatar: string;
}

// React Native compatible image type
export interface ImageAsset {
  uri: string;
  type: string;
  name: string;
}

// API Error types
export interface APIError {
  message: string;
  status?: number;
  field_errors?: Record<string, string[]>;
}

// Validation result type
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

// Enhanced user data with computed properties
export interface EnhancedUser extends User {
  displayName: string;
  hasAvatar: boolean;
}

/**
 * Profile API Service
 * Provides methods for profile management operations
 */
export const profileAPI = {
  /**
   * Get user profile information
   * @returns Promise<User> User profile data
   */
  async getProfile(): Promise<User> {
    try {
      const response = await apiClient.get('/auth/profile/');
      return response.data;
    } catch (error) {
      console.error('Get profile error:', error);
      throw this.handleError(error);
    }
  },

  /**
   * Update user profile information
   * @param data Profile update data
   * @returns Promise<User> Updated user profile data
   */
  async updateProfile(data: ProfileUpdateRequest): Promise<User> {
    try {
      const response = await apiClient.patch('/auth/profile/update/', data);
      return response.data;
    } catch (error) {
      console.error('Update profile error:', error);
      throw this.handleError(error);
    }
  },

  /**
   * Get extended user profile details
   * Uses the backend UserProfile endpoint for extended information
   * @returns Promise<UserProfile> Extended profile data
   */
  async getProfileDetails(): Promise<UserProfile | null> {
    try {
      // Use the backend UserProfile endpoint that exists
      const response = await apiClient.get('/auth/profile/details/');
      return response.data;
    } catch (error: any) {
      // If endpoint doesn't exist (404), return null gracefully
      if (error.response?.status === 404) {
        console.warn('getProfileDetails: Profile details endpoint not found, returning null');
        return null;
      }
      console.error('Get profile details error:', error);
      throw error;
    }
  },

  /**
   * Update extended user profile details
   * Uses the backend UserProfile update endpoint for extended information
   * @param data Profile details update data
   * @returns Promise<UserProfile> Updated profile details
   */
  async updateProfileDetails(data: ProfileDetailsUpdateRequest): Promise<UserProfile | null> {
    try {
      // Use the backend UserProfile update endpoint that exists
      const response = await apiClient.patch('/auth/profile/details/', data);
      return response.data;
    } catch (error: any) {
      // If endpoint doesn't exist (404), fall back to main profile endpoint
      if (error.response?.status === 404) {
        console.warn('updateProfileDetails: Profile details endpoint not found, using main profile endpoint');

        // Map some fields to the main profile endpoint
        const basicFields: ProfileUpdateRequest = {};
        if (data.business_name) basicFields.first_name = data.business_name; // Temporary mapping

        if (Object.keys(basicFields).length > 0) {
          await this.updateProfile(basicFields);
        }

        return null;
      }
      console.error('Update profile details error:', error);
      throw error;
    }
  },

  /**
   * Upload user avatar image
   * Uses the profile update endpoint with multipart/form-data
   * @param imageAsset React Native image asset
   * @returns Promise<string> Avatar URL
   */
  async uploadAvatar(imageAsset: ImageAsset): Promise<string> {
    try {
      const formData = new FormData();
      formData.append('avatar', {
        uri: imageAsset.uri,
        type: imageAsset.type,
        name: imageAsset.name,
      } as any);

      // Use the profile update endpoint for avatar upload
      const response = await apiClient.patch('/auth/profile/update/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      // Return the avatar URL from response or fallback to original URI
      return response.data.avatar || imageAsset.uri;
    } catch (error) {
      console.error('Upload avatar error:', error);
      throw this.handleError(error);
    }
  },

  /**
   * Delete user avatar image
   * Uses the profile update endpoint to clear the avatar
   * @returns Promise<void>
   */
  async deleteAvatar(): Promise<void> {
    try {
      // Use the profile update endpoint to clear avatar
      await apiClient.patch('/auth/profile/update/', { avatar: null });
    } catch (error) {
      console.error('Delete avatar error:', error);
      throw this.handleError(error);
    }
  },



  /**
   * Enhanced error handler for profile API calls
   * @param error API error object
   * @returns Formatted error object
   */
  handleError(error: any): APIError {
    if (error.response) {
      return {
        message: error.response.data?.detail || error.response.data?.message || 'An error occurred',
        status: error.response.status,
        field_errors: error.response.data?.errors || error.response.data,
      };
    } else if (error.request) {
      return {
        message: 'Network error - please check your connection',
        status: 0,
      };
    } else {
      return {
        message: error.message || 'An unexpected error occurred',
      };
    }
  },

  /**
   * Transform user data for display purposes
   * @param user User data from API
   * @returns Transformed user data
   */
  transformUserData(user: User): User & { displayName: string; hasAvatar: boolean } {
    return {
      ...user,
      displayName: user.full_name || `${user.first_name} ${user.last_name}`.trim() || user.username,
      hasAvatar: Boolean(user.avatar),
    };
  },

  /**
   * Check if profile is complete
   * @param user User profile data
   * @returns Boolean indicating if profile is complete
   */
  isProfileComplete(user: User): boolean {
    const requiredFields = ['first_name', 'last_name', 'email'];
    return requiredFields.every(field => Boolean(user[field as keyof User]));
  },

  /**
   * Validate profile data before submission
   * @param data Profile data to validate
   * @returns Validation result with errors
   */
  validateProfileData(data: ProfileUpdateRequest): ValidationResult {
    const errors: string[] = [];

    // Validate phone number format
    if (data.phone && !data.phone.startsWith('+')) {
      errors.push('Phone number must include country code (e.g., +1234567890)');
    }

    // Validate bio length
    if (data.bio && data.bio.length > 500) {
      errors.push('Bio cannot exceed 500 characters');
    }

    // Validate date of birth format
    if (data.date_of_birth && !/^\d{4}-\d{2}-\d{2}$/.test(data.date_of_birth)) {
      errors.push('Date of birth must be in YYYY-MM-DD format');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },
};

export default profileAPI;
