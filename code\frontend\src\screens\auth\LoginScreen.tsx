/**
 * Login Screen - EPIC-01 Approach
 * User authentication screen following EPIC-01 architectural patterns
 * 
 * Features:
 * - Theme context integration with useTheme hook
 * - SafeAreaScreen wrapper component
 * - Atomic design components (Box, Button, Input)
 * - Proper form validation and interaction tracking
 * - New color palette implementation
 * - Responsive utilities integration
 */

import { useNavigation } from '@react-navigation/native';
import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';

// Temporarily using existing components to test build
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Text } from '../../components/ui/Text';
import { SocialButton } from '../../components/ui/SocialButton';
import { useTheme } from '../../contexts/ThemeContext';
import { useAuth } from '../../contexts/AuthContext';
import TestAccountsPanel from '../../components/dev/TestAccountsPanel';

interface FormData {
  email: string;
  password: string;
}

interface FormErrors {
  email?: string;
  password?: string;
}

export const LoginScreen: React.FC = () => {
  const navigation = useNavigation();
  const { colors } = useTheme();
  const { login: contextLogin, isLoading } = useAuth();
  const styles = createStyles(colors);

  const [formData, setFormData] = useState<FormData>({
    email: '',
    password: '',
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [hasInteracted, setHasInteracted] = useState({
    email: false,
    password: false,
  });
  const [showTestAccountsPanel, setShowTestAccountsPanel] = useState(false);
  const [socialLoading, setSocialLoading] = useState({
    google: false,
    apple: false,
  });

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setHasInteracted(prev => ({ ...prev, [field]: true }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleLogin = async () => {
    // Mark all fields as interacted for validation display
    setHasInteracted({
      email: true,
      password: true,
    });

    if (!validateForm()) {
      return;
    }

    try {
      const success = await contextLogin(formData.email, formData.password);
      
      if (success) {
        // Navigation will be handled automatically by the auth context
        console.log('Login successful');
      } else {
        Alert.alert('Login Failed', 'Invalid email or password');
      }
    } catch (error) {
      console.error('Login error:', error);
      Alert.alert('Login Failed', 'An error occurred during login');
    }
  };

  const handleForgotPassword = () => {
    // TODO: Navigate to forgot password screen
    Alert.alert('Forgot Password', 'Password reset functionality will be implemented soon');
  };

  const handleSignUp = () => {
    navigation.navigate('Register' as never);
  };

  const handleGoogleSignIn = async () => {
    setSocialLoading(prev => ({ ...prev, google: true }));

    try {
      // TODO: Implement actual Google Sign-In integration
      Alert.alert(
        'Google Sign-In',
        'Google Sign-In integration will be implemented in the next phase.',
        [{ text: 'OK', onPress: () => setSocialLoading(prev => ({ ...prev, google: false })) }]
      );
    } catch (error) {
      console.error('Google sign-in error:', error);
      setSocialLoading(prev => ({ ...prev, google: false }));
      Alert.alert('Error', 'Google sign-in failed. Please try again.');
    }
  };

  const handleAppleSignIn = async () => {
    setSocialLoading(prev => ({ ...prev, apple: true }));

    try {
      // TODO: Implement actual Apple Sign-In integration
      Alert.alert(
        'Apple Sign-In',
        'Apple Sign-In integration will be implemented in the next phase.',
        [{ text: 'OK', onPress: () => setSocialLoading(prev => ({ ...prev, apple: false })) }]
      );
    } catch (error) {
      console.error('Apple sign-in error:', error);
      setSocialLoading(prev => ({ ...prev, apple: false }));
      Alert.alert('Error', 'Apple sign-in failed. Please try again.');
    }
  };

  return (
    <View
      style={{ flex: 1, backgroundColor: colors.background.primary }}
      testID="login-container"
    >
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.content} testID="login-form-box">
          {/* Header */}
          <View style={styles.header}>
            <Text
              variant="h1"
              style={styles.title}
              testID="login-title"
            >
              Welcome Back
            </Text>
            <Text
              variant="body"
              style={styles.subtitle}
              testID="login-subtitle"
            >
              Sign in to your Vierla account
            </Text>
          </View>

          {/* Form */}
          <View style={styles.form}>
            <Input
              label="Email"
              placeholder="Enter your email"
              value={formData.email}
              onChangeText={(value) => handleInputChange('email', value)}
              error={hasInteracted.email ? errors.email : undefined}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
              leftIcon="mail-outline"
              testID="email-input"
            />

            <Input
              label="Password"
              placeholder="Enter your password"
              value={formData.password}
              onChangeText={(value) => handleInputChange('password', value)}
              error={hasInteracted.password ? errors.password : undefined}
              secureTextEntry
              leftIcon="lock-closed-outline"
              testID="password-input"
            />

            <Button
              onPress={handleLogin}
              loading={isLoading}
              style={styles.loginButton}
              testID="login-button"
            >
              Sign In
            </Button>

            <TouchableOpacity
              onPress={handleForgotPassword}
              style={styles.forgotPasswordLink}
              testID="forgot-password-link"
            >
              <Text variant="body" style={styles.forgotPasswordText}>
                Forgot Password?
              </Text>
            </TouchableOpacity>
          </View>

          {/* Social Login Section */}
          <View style={styles.socialSection}>
            <View style={styles.dividerContainer}>
              <View style={styles.dividerLine} />
              <Text variant="body" style={styles.dividerText}>
                Or continue with
              </Text>
              <View style={styles.dividerLine} />
            </View>

            <View style={styles.socialButtons}>
              <SocialButton
                provider="google"
                onPress={handleGoogleSignIn}
                loading={socialLoading.google}
                disabled={isLoading}
                style={styles.socialButton}
                testID="google-signin-button"
              />
              <SocialButton
                provider="apple"
                onPress={handleAppleSignIn}
                loading={socialLoading.apple}
                disabled={isLoading}
                style={styles.socialButton}
                testID="apple-signin-button"
              />
            </View>
          </View>

          {/* Footer */}
          <View style={styles.footer}>
            <Text variant="body" style={styles.footerText}>
              Don't have an account?{' '}
              <Text
                variant="body"
                style={styles.signUpLink}
                onPress={handleSignUp}
                testID="sign-up-link"
              >
                Sign Up
              </Text>
            </Text>

            {/* Development Test Accounts Button */}
            {__DEV__ && (
              <TouchableOpacity
                onPress={() => setShowTestAccountsPanel(true)}
                style={styles.testAccountsButton}
                testID="test-accounts-button"
              >
                <Text variant="body" style={styles.testAccountsText}>
                  🧪 Login with Test Accounts
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </ScrollView>

      {/* Test Accounts Panel */}
      {showTestAccountsPanel && (
        <TestAccountsPanel
          visible={showTestAccountsPanel}
          onClose={() => setShowTestAccountsPanel(false)}
        />
      )}
    </View>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    scrollContent: {
      flexGrow: 1,
      justifyContent: 'center',
    },
    content: {
      padding: 24,
    },
    header: {
      marginBottom: 32,
    },
    title: {
      fontSize: 32,
      fontWeight: 'bold',
      color: '#000000', // Black color as requested
      textAlign: 'center',
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      color: '#000000', // Black color as requested
      textAlign: 'center',
    },
    form: {
      gap: 16,
      marginBottom: 32,
    },
    loginButton: {
      backgroundColor: colors.primary, // Forest Green (#364035)
      marginTop: 8,
    },
    forgotPasswordLink: {
      alignItems: 'center',
      padding: 8,
      borderWidth: 1,
      borderColor: colors.primary, // Forest Green border as requested
      borderRadius: 8,
      marginTop: 8,
    },
    forgotPasswordText: {
      color: colors.primary, // Changed to Forest Green to match border
      fontSize: 14,
      fontWeight: '600',
    },
    socialSection: {
      marginBottom: 32,
    },
    dividerContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 24,
    },
    dividerLine: {
      flex: 1,
      height: 1,
      backgroundColor: colors.text.secondary,
      opacity: 0.3,
    },
    dividerText: {
      marginHorizontal: 16,
      color: colors.text.secondary,
      fontSize: 14,
    },
    socialButtons: {
      gap: 12,
    },
    socialButton: {
      marginBottom: 0,
    },
    footer: {
      alignItems: 'center',
    },
    footerText: {
      color: colors.text.secondary, // Sage Green (#8B9A8C)
      fontSize: 14,
      textAlign: 'center',
    },
    signUpLink: {
      color: colors.accent, // Rich Gold (#B8956A)
      fontWeight: '600',
    },
    testAccountsButton: {
      marginTop: 16,
      padding: 12,
      backgroundColor: colors.extended.lightest, // Light background
      borderRadius: 8,
      borderWidth: 1,
      borderColor: colors.extended.sageVariant,
      alignItems: 'center',
    },
    testAccountsText: {
      color: colors.text.tertiary, // Sage Green
      fontSize: 14,
      fontWeight: '500',
    },
  });
