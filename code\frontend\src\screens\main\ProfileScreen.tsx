/**
 * Profile Screen
 * User profile and settings
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Alert,
  RefreshControl,
  Text as RNText,
  BackHandler
} from 'react-native';
import { NavigationProp, RouteProp, useFocusEffect } from '@react-navigation/native';
import { ProfileStackParamList } from '../../navigation/MainNavigator';

import { Text } from '../../components';
import { SafeAreaViewWrapper } from '../../components/ui/SafeAreaViewWrapper';
import { AvatarUpload, ProfileDisplay, ProfileForm } from '../../components/profile';
import { profileAPI, User, UserProfile, ProfileUpdateRequest, ProfileDetailsUpdateRequest } from '../../services/api/profile';
import { AnimatedCard } from '../../components/ui/AnimatedCard';
import { FadeTransition } from '../../components/ui/FadeTransition';
import { LoadingAnimation } from '../../components/ui/LoadingAnimation';

interface ProfileScreenProps {
  navigation: NavigationProp<ProfileStackParamList>;
  route: RouteProp<ProfileStackParamList, 'Profile'>;
}

export const ProfileScreen: React.FC<ProfileScreenProps> = ({ navigation, route }) => {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<any>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isAvatarUploadVisible, setIsAvatarUploadVisible] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    loadProfileData();
  }, []);

  // Handle screen focus and navigation state
  useFocusEffect(
    useCallback(() => {
      // Refresh data when screen comes into focus
      loadProfileData();

      // Handle back button on Android
      const onBackPress = () => {
        if (isEditing) {
          // If editing, show confirmation dialog
          Alert.alert(
            'Discard Changes?',
            'You have unsaved changes. Are you sure you want to go back?',
            [
              { text: 'Cancel', style: 'cancel' },
              {
                text: 'Discard',
                style: 'destructive',
                onPress: () => {
                  handleCancel();
                  navigation.goBack();
                }
              }
            ]
          );
          return true; // Prevent default back action
        }
        return false; // Allow default back action
      };

      const backHandler = BackHandler.addEventListener('hardwareBackPress', onBackPress);

      return () => backHandler.remove();
    }, [isEditing, navigation])
  );

  const onRefresh = async () => {
    setIsRefreshing(true);
    await loadProfileData();
    setIsRefreshing(false);
  };

  const loadProfileData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load user profile data
      const userData = await profileAPI.getProfile();

      // For now, create a mock profile since backend doesn't have extended profile endpoint
      const profileData = await profileAPI.getProfileDetails() || {
        address: '',
        city: '',
        state: '',
        zip_code: '',
        country: 'Canada',
        business_name: '',
        business_description: '',
        years_of_experience: 0,
        website: '',
        instagram: '',
        facebook: '',
        search_radius: 25,
        auto_accept_bookings: false,
        show_phone_publicly: true,
        show_email_publicly: false,
        allow_reviews: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Transform user data for enhanced display
      const transformedUser = profileAPI.transformUserData(userData);

      setUser(transformedUser);
      setProfile(profileData);

      // Check if profile is complete and show helpful message
      const isComplete = profileAPI.isProfileComplete(userData);
      if (!isComplete) {
        console.log('Profile is incomplete. Consider prompting user to complete it.');
      }

      setFormData({
        first_name: userData.first_name,
        last_name: userData.last_name,
        phone: userData.phone || '',
        bio: userData.bio || '',
        date_of_birth: userData.date_of_birth || '',
        address: profileData.address || '',
        city: profileData.city || '',
        state: profileData.state || '',
        zip_code: profileData.zip_code || '',
        country: profileData.country || 'Canada',
        business_name: profileData.business_name || '',
        business_description: profileData.business_description || '',
        years_of_experience: profileData.years_of_experience || 0,
        website: profileData.website || '',
        instagram: profileData.instagram || '',
        facebook: profileData.facebook || '',
      });
    } catch (err: any) {
      console.error('Error loading profile:', err);
      setError('Error loading profile. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Navigation helpers
  const navigateToSettings = useCallback(() => {
    navigation.navigate('Settings');
  }, [navigation]);

  const handleDeepLinkNavigation = useCallback((params?: any) => {
    // Handle deep link navigation to specific profile sections
    if (params?.section === 'edit') {
      setIsEditing(true);
    } else if (params?.section === 'avatar') {
      setIsAvatarUploadVisible(true);
    }
  }, []);

  // Handle route params for deep linking
  useEffect(() => {
    if (route.params) {
      handleDeepLinkNavigation(route.params);
    }
  }, [route.params, handleDeepLinkNavigation]);

  const handleSave = async (data: any) => {
    try {
      setIsSaving(true);
      setErrors({});

      // Use the enhanced validation from the API service
      const profileUpdateData: ProfileUpdateRequest = {
        first_name: data.first_name,
        last_name: data.last_name,
        phone: data.phone,
        bio: data.bio,
        date_of_birth: data.date_of_birth,
      };

      const validationResult = profileAPI.validateProfileData(profileUpdateData);

      // Additional custom validation for required fields and business data
      const validationErrors: Record<string, string> = {};
      if (!data.first_name?.trim()) {
        validationErrors.first_name = 'First name is required';
      }
      if (!data.last_name?.trim()) {
        validationErrors.last_name = 'Last name is required';
      }
      if (data.website && !/^https?:\/\/.+/.test(data.website)) {
        validationErrors.website = 'Please enter a valid website URL';
      }
      if (data.years_of_experience && (data.years_of_experience < 0 || data.years_of_experience > 50)) {
        validationErrors.years_of_experience = 'Years of experience must be between 0 and 50';
      }

      // Combine API validation errors with custom validation
      if (!validationResult.isValid) {
        validationResult.errors.forEach((error, index) => {
          validationErrors[`validation_${index}`] = error;
        });
      }

      if (Object.keys(validationErrors).length > 0) {
        setErrors(validationErrors);
        return;
      }

      // Update user profile using the already declared profileUpdateData

      const profileDetailsData: ProfileDetailsUpdateRequest = {
        address: data.address,
        city: data.city,
        state: data.state,
        zip_code: data.zip_code,
        country: data.country,
        business_name: data.business_name,
        business_description: data.business_description,
        years_of_experience: data.years_of_experience,
        website: data.website,
        instagram: data.instagram,
        facebook: data.facebook,
      };

      // Update user profile (this works with current backend)
      const updatedUser = await profileAPI.updateProfile(profileUpdateData);

      // Update profile details (returns null for now since backend endpoint doesn't exist)
      const updatedProfile = await profileAPI.updateProfileDetails(profileDetailsData);

      setUser(updatedUser);

      // If profile update returns null, keep the current profile with updated form data
      if (updatedProfile) {
        setProfile(updatedProfile);
      } else {
        // Update local profile state with form data since backend doesn't support extended profile yet
        setProfile(prev => prev ? { ...prev, ...profileDetailsData } : null);
      }

      setIsEditing(false);

      // Enhanced success feedback
      Alert.alert(
        '✅ Success',
        'Your profile has been updated successfully!',
        [{ text: 'OK', style: 'default' }],
        { cancelable: true }
      );
    } catch (err: any) {
      console.error('Error saving profile:', err);

      // Handle the new API error structure
      if (err.field_errors) {
        // Convert field errors to the format expected by the form
        const formErrors: Record<string, string> = {};
        Object.entries(err.field_errors).forEach(([field, errors]) => {
          if (Array.isArray(errors) && errors.length > 0) {
            formErrors[field] = errors[0];
          } else if (typeof errors === 'string') {
            formErrors[field] = errors;
          }
        });
        setErrors(formErrors);

        Alert.alert(
          '⚠️ Validation Error',
          err.message || 'Please check the highlighted fields and try again.',
          [{ text: 'OK', style: 'default' }]
        );
      } else if (err.status === 0) {
        Alert.alert(
          '🌐 Network Error',
          'Please check your internet connection and try again.',
          [{ text: 'Retry', style: 'default' }]
        );
      } else {
        Alert.alert(
          '❌ Error',
          err.message || 'Failed to update profile. Please try again.',
          [{ text: 'Retry', style: 'default' }]
        );
      }
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setErrors({});
    // Reset form data
    if (user && profile) {
      setFormData({
        first_name: user.first_name,
        last_name: user.last_name,
        phone: user.phone || '',
        bio: user.bio || '',
        date_of_birth: user.date_of_birth || '',
        address: profile.address || '',
        city: profile.city || '',
        state: profile.state || '',
        zip_code: profile.zip_code || '',
        country: profile.country || 'Canada',
        business_name: profile.business_name || '',
        business_description: profile.business_description || '',
        years_of_experience: profile.years_of_experience || 0,
        website: profile.website || '',
        instagram: profile.instagram || '',
        facebook: profile.facebook || '',
      });
    }
  };

  const handleAvatarPress = () => {
    setIsAvatarUploadVisible(true);
  };

  const handleAvatarUpload = async (imageUri: string) => {
    try {
      setIsLoading(true);

      // Upload avatar using the enhanced API
      const updatedAvatarUri = await profileAPI.uploadAvatar(imageUri);

      // Update local state with new avatar URL
      if (user) {
        setUser({ ...user, avatar: updatedAvatarUri });
      }
      setIsAvatarUploadVisible(false);

      Alert.alert(
        '✅ Success',
        'Profile picture updated successfully!',
        [{ text: 'OK', style: 'default' }]
      );
    } catch (error: any) {
      console.error('Avatar upload error:', error);
      Alert.alert(
        '❌ Error',
        error.message || 'Failed to update profile picture. Please try again.',
        [{ text: 'OK', style: 'default' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleAvatarRemove = async () => {
    try {
      setIsLoading(true);

      // Remove avatar using profile API
      await profileAPI.deleteAvatar();

      // Update local state to remove avatar
      if (user) {
        setUser({ ...user, avatar: undefined });
      }
      setIsAvatarUploadVisible(false);

      Alert.alert(
        '✅ Success',
        'Profile picture removed successfully!',
        [{ text: 'OK', style: 'default' }]
      );
    } catch (error: any) {
      console.error('Avatar remove error:', error);
      Alert.alert(
        '❌ Error',
        error.message || 'Failed to remove profile picture. Please try again.',
        [{ text: 'OK', style: 'default' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <LoadingAnimation size="large" testID="loading-animation" />
          <RNText style={styles.loadingText} testID="loading-text">Loading profile...</RNText>
        </View>
      </View>
    );
  }

  if (error) {
    return (
      <SafeAreaViewWrapper style={styles.container}>
        <View style={styles.errorContainer} testID="error-container">
          <Text style={styles.errorText} testID="error-text">Error loading profile</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadProfileData} testID="retry-button">
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaViewWrapper>
    );
  }

  if (!user || !profile) {
    return null;
  }

  return (
    <SafeAreaViewWrapper style={styles.container} testID="profile-screen" accessibilityRole="main">
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={onRefresh}
            tintColor="#364035"
            colors={['#364035']}
          />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <RNText style={styles.headerTitle}>Profile</RNText>
          <View style={styles.headerButtons}>
            <TouchableOpacity
              style={styles.settingsButton}
              onPress={navigateToSettings}
              testID="settings-button"
              accessibilityLabel="Settings"
            >
              <RNText style={styles.settingsButtonText}>⚙️</RNText>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.editButton}
              onPress={() => setIsEditing(!isEditing)}
              testID="edit-profile-button"
              accessibilityLabel="Edit profile"
            >
              <RNText style={styles.editButtonText}>
                {isEditing ? 'Cancel' : 'Edit'}
              </RNText>
            </TouchableOpacity>
          </View>
        </View>

        {/* User Info Header */}
        <View style={styles.userHeader}>
          <TouchableOpacity onPress={handleAvatarPress} testID="user-avatar" accessibilityLabel="User avatar">
            {user.avatar ? (
              <Image
                source={{ uri: user.avatar }}
                style={styles.avatar}
              />
            ) : (
              <View style={[styles.avatar, styles.defaultAvatar]}>
                <RNText style={styles.avatarText}>
                  {user.first_name?.[0]?.toUpperCase()}{user.last_name?.[0]?.toUpperCase()}
                </RNText>
              </View>
            )}
          </TouchableOpacity>
          <View style={styles.userInfo}>
            <RNText style={styles.userName}>{user.full_name}</RNText>
            <RNText style={styles.userEmail}>{user.email}</RNText>
            <View style={styles.roleContainer}>
              <RNText style={styles.userRole}>
                {user.role === 'customer' ? 'Customer' : 'Service Provider'}
              </RNText>
            </View>
          </View>
        </View>

        {/* Profile Content with Smooth Transitions */}
        <AnimatedCard style={{ marginTop: 16 }}>
          <FadeTransition visible={isEditing} testID="profile-form-transition">
            {isEditing && (
              <ProfileForm
                user={user}
                profile={profile}
                formData={formData}
                setFormData={setFormData}
                errors={errors}
                onSave={handleSave}
                onCancel={handleCancel}
                isLoading={isSaving}
              />
            )}
          </FadeTransition>

          <FadeTransition visible={!isEditing} testID="profile-display-transition">
            {!isEditing && (
              <ProfileDisplay user={user} profile={profile} />
            )}
          </FadeTransition>
        </AnimatedCard>
      </ScrollView>

      {/* Avatar Upload Modal */}
      <AvatarUpload
        currentAvatar={user?.avatar}
        onUpload={handleAvatarUpload}
        onRemove={handleAvatarRemove}
        isVisible={isAvatarUploadVisible}
        onClose={() => setIsAvatarUploadVisible(false)}
        isPremium={false} // This would come from user subscription status
      />

      {/* Loading Overlay for Saving */}
      {isSaving && (
        <View style={styles.loadingOverlay}>
          <AnimatedCard style={styles.loadingCard}>
            <LoadingAnimation
              type="breathe"
              size="medium"
              text="Saving profile..."
            />
          </AnimatedCard>
        </View>
      )}
    </SafeAreaViewWrapper>
  );
};







const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F4F1E8',
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#364035',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorText: {
    fontSize: 16,
    color: '#DC2626',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: '#364035',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2D2A26',
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  settingsButton: {
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  settingsButtonText: {
    fontSize: 16,
  },
  editButton: {
    backgroundColor: '#364035',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  editButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  userHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 24,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#E5E7EB',
  },
  defaultAvatar: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#8B9A8C',
  },
  avatarText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  userInfo: {
    marginLeft: 16,
    flex: 1,
  },
  userName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2D2A26',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 16,
    color: '#6B7280',
    marginBottom: 8,
  },
  roleContainer: {
    backgroundColor: '#8B9A8C',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  userRole: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  profileContent: {
    padding: 24,
  },
  section: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2D2A26',
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
    flex: 1,
  },
  infoValue: {
    fontSize: 14,
    color: '#2D2A26',
    flex: 2,
    textAlign: 'right',
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#2D2A26',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    color: '#2D2A26',
    backgroundColor: '#FFFFFF',
  },
  textArea: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    color: '#2D2A26',
    backgroundColor: '#FFFFFF',
    minHeight: 100,
    textAlignVertical: 'top',
  },
  inputError: {
    borderColor: '#DC2626',
  },
  actionButtons: {
    marginTop: 24,
    gap: 12,
  },
  button: {
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  saveButton: {
    backgroundColor: '#364035',
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButton: {
    backgroundColor: '#E5E7EB',
  },
  cancelButtonText: {
    color: '#2D2A26',
    fontSize: 16,
    fontWeight: '600',
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  settingText: {
    fontSize: 16,
    color: '#2D2A26',
  },
  settingArrow: {
    fontSize: 18,
    color: '#9CA3AF',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  loadingCard: {
    padding: 32,
    alignItems: 'center',
    minWidth: 200,
  },
});
