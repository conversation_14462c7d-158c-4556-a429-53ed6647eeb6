/**
 * App Navigator
 * Main navigation container that handles auth state and routing
 */

import React, { useEffect, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Linking } from 'react-native';

import { AuthNavigator } from './AuthNavigator';
import { MainNavigator } from './MainNavigator';
import { LoadingScreen } from '../screens/LoadingScreen';
import { AuthProvider } from '../contexts/AuthContext';
import { NavigationProvider } from '../contexts/NavigationContext';

export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  Loading: undefined;
};

const Stack = createStackNavigator<RootStackParamList>();

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

// Deep linking configuration
const linking = {
  prefixes: ['vierla://', 'https://vierla.app'],
  config: {
    screens: {
      Auth: {
        screens: {
          Login: 'login',
          Register: 'register',
          ForgotPassword: 'forgot-password',
        },
      },
      Main: {
        screens: {
          Home: 'home',
          Services: 'services',
          Bookings: 'bookings',
          ProfileStack: {
            screens: {
              Profile: 'profile',
              Settings: 'settings',
            },
          },
        },
      },
    },
  },
};

export const AppNavigator: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    checkAppStatus();
  }, []);

  const checkAppStatus = async () => {
    try {
      // Check authentication status
      const accessToken = await AsyncStorage.getItem('access_token');
      const user = await AsyncStorage.getItem('user');

      setIsAuthenticated(!!(accessToken && user));
    } catch (error) {
      console.error('Error checking app status:', error);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <NavigationProvider onStateRefresh={checkAppStatus}>
          <NavigationContainer linking={linking}>
            <Stack.Navigator
              screenOptions={{
                headerShown: false,
                cardStyle: { backgroundColor: '#FFFFFF' },
                safeAreaInsets: { top: 0, bottom: 0, left: 0, right: 0 }, // Disable SafeAreaView in React Navigation
                // Additional options to prevent screen container issues
                animationEnabled: false, // Disable animations to reduce complexity
                presentation: 'card',
              }}
            >
              {isAuthenticated ? (
                <Stack.Screen
                  name="Main"
                  component={MainNavigator}
                  options={{
                    headerShown: false,
                    animationEnabled: false
                  }}
                />
              ) : (
                <Stack.Screen
                  name="Auth"
                  component={AuthNavigator}
                  options={{
                    headerShown: false,
                    animationEnabled: false
                  }}
                />
              )}
            </Stack.Navigator>
          </NavigationContainer>
        </NavigationProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
};
