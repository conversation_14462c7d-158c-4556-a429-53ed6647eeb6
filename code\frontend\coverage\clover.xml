<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1754590690407" clover="3.2.0">
  <project timestamp="1754590690408" name="All files">
    <metrics statements="3468" coveredstatements="222" conditionals="2589" coveredconditionals="98" methods="926" coveredmethods="22" elements="6983" coveredelements="342" complexity="0" loc="3468" ncloc="3468" packages="22" files="109" classes="109"/>
    <package name="components">
      <metrics statements="211" coveredstatements="32" conditionals="211" coveredconditionals="4" methods="67" coveredmethods="1"/>
      <file name="Button.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\Button.tsx">
        <metrics statements="5" coveredstatements="2" conditionals="14" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="27" count="1" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
      </file>
      <file name="CategoryCard.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\CategoryCard.tsx">
        <metrics statements="24" coveredstatements="4" conditionals="24" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="31" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="103" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="113" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="124" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="160" count="1" type="stmt"/>
      </file>
      <file name="FilterPanel.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\FilterPanel.tsx">
        <metrics statements="52" coveredstatements="4" conditionals="37" coveredconditionals="0" methods="17" coveredmethods="0"/>
        <line num="48" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="75" count="1" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="140" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="150" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="153" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="199" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="218" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="234" count="0" type="stmt"/>
        <line num="236" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="240" count="0" type="stmt"/>
        <line num="292" count="1" type="stmt"/>
      </file>
      <file name="Input.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\Input.tsx">
        <metrics statements="11" coveredstatements="2" conditionals="24" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="28" count="1" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
      </file>
      <file name="LoadingSpinner.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\LoadingSpinner.tsx">
        <metrics statements="5" coveredstatements="0" conditionals="3" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="16" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="24" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
      </file>
      <file name="SearchBar.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\SearchBar.tsx">
        <metrics statements="35" coveredstatements="2" conditionals="18" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="31" count="1" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="54" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="163" count="1" type="stmt"/>
      </file>
      <file name="ServiceCard.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\ServiceCard.tsx">
        <metrics statements="22" coveredstatements="3" conditionals="19" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="16" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="62" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="103" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="130" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="202" count="1" type="stmt"/>
      </file>
      <file name="ServiceList.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\ServiceList.tsx">
        <metrics statements="29" coveredstatements="5" conditionals="45" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="13" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="76" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="115" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="126" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="136" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="144" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="156" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="199" count="1" type="stmt"/>
      </file>
      <file name="SortSelector.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\SortSelector.tsx">
        <metrics statements="24" coveredstatements="6" conditionals="23" coveredconditionals="0" methods="11" coveredmethods="0"/>
        <line num="33" count="1" type="stmt"/>
        <line num="93" count="1" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="120" count="1" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="235" count="1" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="285" count="1" type="stmt"/>
      </file>
      <file name="Text.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\Text.tsx">
        <metrics statements="4" coveredstatements="4" conditionals="4" coveredconditionals="4" methods="1" coveredmethods="1"/>
        <line num="16" count="1" type="stmt"/>
        <line num="25" count="2" type="stmt"/>
        <line num="34" count="2" type="stmt"/>
        <line num="41" count="1" type="stmt"/>
      </file>
      <file name="index.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="components.dev">
      <metrics statements="32" coveredstatements="0" conditionals="25" coveredconditionals="0" methods="11" coveredmethods="0"/>
      <file name="TestAccountsPanel.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\dev\TestAccountsPanel.tsx">
        <metrics statements="32" coveredstatements="0" conditionals="25" coveredconditionals="0" methods="11" coveredmethods="0"/>
        <line num="22" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
      </file>
    </package>
    <package name="components.error">
      <metrics statements="59" coveredstatements="0" conditionals="74" coveredconditionals="0" methods="18" coveredmethods="0"/>
      <file name="ErrorBoundary.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\error\ErrorBoundary.tsx">
        <metrics statements="31" coveredstatements="0" conditionals="20" coveredconditionals="0" methods="11" coveredmethods="0"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="127" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="128" count="0" type="stmt"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="132" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="133" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="215" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="217" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
      </file>
      <file name="ErrorDisplay.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\error\ErrorDisplay.tsx">
        <metrics statements="28" coveredstatements="0" conditionals="54" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="187" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="189" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="230" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="232" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\error\index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="components.feedback">
      <metrics statements="52" coveredstatements="0" conditionals="34" coveredconditionals="0" methods="17" coveredmethods="0"/>
      <file name="ToastSystem.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\feedback\ToastSystem.tsx">
        <metrics statements="52" coveredstatements="0" conditionals="34" coveredconditionals="0" methods="17" coveredmethods="0"/>
        <line num="28" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="96" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="187" count="0" type="stmt"/>
        <line num="191" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="228" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="230" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
      </file>
    </package>
    <package name="components.navigation">
      <metrics statements="4" coveredstatements="0" conditionals="9" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="TabBarIcon.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\navigation\TabBarIcon.tsx">
        <metrics statements="4" coveredstatements="0" conditionals="9" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="23" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
      </file>
    </package>
    <package name="components.profile">
      <metrics statements="119" coveredstatements="25" conditionals="128" coveredconditionals="29" methods="42" coveredmethods="5"/>
      <file name="AvatarUpload.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\profile\AvatarUpload.tsx">
        <metrics statements="80" coveredstatements="16" conditionals="40" coveredconditionals="8" methods="16" coveredmethods="2"/>
        <line num="38" count="1" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="77" count="21" type="stmt"/>
        <line num="78" count="21" type="stmt"/>
        <line num="79" count="21" type="stmt"/>
        <line num="80" count="21" type="stmt"/>
        <line num="83" count="21" type="stmt"/>
        <line num="84" count="21" type="stmt"/>
        <line num="87" count="21" type="stmt"/>
        <line num="88" count="21" type="cond" truecount="1" falsecount="1"/>
        <line num="89" count="0" type="stmt"/>
        <line num="103" count="21" type="stmt"/>
        <line num="118" count="21" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="140" count="21" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="154" count="21" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="157" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="160" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="170" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="180" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="194" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="195" count="0" type="stmt"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="199" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="200" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="230" count="21" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="250" count="21" type="stmt"/>
        <line num="425" count="0" type="stmt"/>
        <line num="433" count="0" type="stmt"/>
      </file>
      <file name="ProfileDisplay.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\profile\ProfileDisplay.tsx">
        <metrics statements="8" coveredstatements="8" conditionals="30" coveredconditionals="21" methods="3" coveredmethods="3"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="21" type="stmt"/>
        <line num="31" count="21" type="stmt"/>
        <line num="54" count="21" type="stmt"/>
        <line num="55" count="88" type="stmt"/>
        <line num="67" count="21" type="cond" truecount="1" falsecount="0"/>
        <line num="68" count="381" type="stmt"/>
        <line num="78" count="21" type="stmt"/>
      </file>
      <file name="ProfileForm.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\profile\ProfileForm.tsx">
        <metrics statements="31" coveredstatements="1" conditionals="58" coveredconditionals="0" methods="23" coveredmethods="0"/>
        <line num="32" count="1" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="230" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="239" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\profile\index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="components.provider">
      <metrics statements="382" coveredstatements="0" conditionals="465" coveredconditionals="0" methods="130" coveredmethods="0"/>
      <file name="MultiStepServiceForm.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\provider\MultiStepServiceForm.tsx">
        <metrics statements="118" coveredstatements="0" conditionals="85" coveredconditionals="0" methods="26" coveredmethods="0"/>
        <line num="63" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="105" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="168" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="199" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="200" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="226" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="227" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="234" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="236" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="239" count="0" type="stmt"/>
        <line num="242" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="243" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="249" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="252" count="0" type="stmt"/>
        <line num="255" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="256" count="0" type="stmt"/>
        <line num="257" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="258" count="0" type="stmt"/>
        <line num="259" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="260" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="266" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="267" count="0" type="stmt"/>
        <line num="268" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="269" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="273" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="274" count="0" type="stmt"/>
        <line num="275" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="276" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="286" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="287" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="288" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="295" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="296" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="301" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="302" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="319" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="320" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="330" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="346" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="348" count="0" type="stmt"/>
        <line num="350" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="360" count="0" type="stmt"/>
        <line num="367" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="368" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
      </file>
      <file name="ProgressIndicator.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\provider\ProgressIndicator.tsx">
        <metrics statements="13" coveredstatements="0" conditionals="22" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="29" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="stmt"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="87" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
      </file>
      <file name="ServiceActions.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\provider\ServiceActions.tsx">
        <metrics statements="16" coveredstatements="0" conditionals="32" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="28" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="43" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="154" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
      </file>
      <file name="ServiceCard.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\provider\ServiceCard.tsx">
        <metrics statements="15" coveredstatements="0" conditionals="50" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="27" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
      </file>
      <file name="ServiceFilters.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\provider\ServiceFilters.tsx">
        <metrics statements="36" coveredstatements="0" conditionals="44" coveredconditionals="0" methods="22" coveredmethods="0"/>
        <line num="41" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="92" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
      </file>
      <file name="ServiceForm.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\provider\ServiceForm.tsx">
        <metrics statements="74" coveredstatements="0" conditionals="103" coveredconditionals="0" methods="15" coveredmethods="0"/>
        <line num="47" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="76" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="103" count="0" type="stmt"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="112" count="0" type="stmt"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="127" count="0" type="stmt"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="136" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="147" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="184" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="195" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="196" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="409" count="0" type="stmt"/>
      </file>
      <file name="ServiceGrid.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\provider\ServiceGrid.tsx">
        <metrics statements="46" coveredstatements="0" conditionals="43" coveredconditionals="0" methods="23" coveredmethods="0"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="93" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="133" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="162" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
      </file>
      <file name="ServiceList.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\provider\ServiceList.tsx">
        <metrics statements="44" coveredstatements="0" conditionals="40" coveredconditionals="0" methods="23" coveredmethods="0"/>
        <line num="36" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="60" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="86" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="121" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="150" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
      </file>
      <file name="ServiceStats.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\provider\ServiceStats.tsx">
        <metrics statements="15" coveredstatements="0" conditionals="38" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="27" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="112" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="134" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
      </file>
      <file name="ViewToggle.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\provider\ViewToggle.tsx">
        <metrics statements="5" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="24" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\provider\index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="components.provider.steps">
      <metrics statements="59" coveredstatements="0" conditionals="115" coveredconditionals="0" methods="26" coveredmethods="0"/>
      <file name="BasicInfoStep.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\provider\steps\BasicInfoStep.tsx">
        <metrics statements="10" coveredstatements="0" conditionals="22" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="33" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
      </file>
      <file name="DetailsStep.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\provider\steps\DetailsStep.tsx">
        <metrics statements="14" coveredstatements="0" conditionals="20" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="30" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
      </file>
      <file name="PricingStep.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\provider\steps\PricingStep.tsx">
        <metrics statements="14" coveredstatements="0" conditionals="35" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="30" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
      </file>
      <file name="ReviewStep.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\provider\steps\ReviewStep.tsx">
        <metrics statements="21" coveredstatements="0" conditionals="38" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="36" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="66" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\provider\steps\index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="components.ui">
      <metrics statements="499" coveredstatements="100" conditionals="493" coveredconditionals="25" methods="117" coveredmethods="11"/>
      <file name="Alert.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\ui\Alert.tsx">
        <metrics statements="31" coveredstatements="8" conditionals="51" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="32" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="72" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="95" count="0" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="115" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="184" count="1" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="188" count="1" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="192" count="1" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="196" count="1" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
      </file>
      <file name="AnimatedCard.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\ui\AnimatedCard.tsx">
        <metrics statements="33" coveredstatements="14" conditionals="31" coveredconditionals="5" methods="4" coveredmethods="1"/>
        <line num="27" count="1" type="stmt"/>
        <line num="36" count="21" type="stmt"/>
        <line num="37" count="21" type="stmt"/>
        <line num="40" count="21" type="stmt"/>
        <line num="41" count="21" type="stmt"/>
        <line num="44" count="21" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="47" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="52" count="0" type="stmt"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="63" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="76" count="21" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="79" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="83" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="84" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="95" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="108" count="21" type="stmt"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="110" count="0" type="stmt"/>
        <line num="114" count="21" type="stmt"/>
        <line num="130" count="21" type="stmt"/>
        <line num="141" count="21" type="stmt"/>
        <line num="146" count="21" type="stmt"/>
        <line num="150" count="21" type="cond" truecount="2" falsecount="2"/>
        <line num="151" count="0" type="stmt"/>
        <line num="165" count="21" type="stmt"/>
      </file>
      <file name="Badge.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\ui\Badge.tsx">
        <metrics statements="16" coveredstatements="4" conditionals="8" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="31" count="1" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="176" count="1" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="192" count="1" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="208" count="1" type="stmt"/>
        <line num="213" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="215" count="0" type="stmt"/>
      </file>
      <file name="Box.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\ui\Box.tsx">
        <metrics statements="7" coveredstatements="1" conditionals="8" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="31" count="1" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
      </file>
      <file name="Button.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\ui\Button.tsx">
        <metrics statements="48" coveredstatements="4" conditionals="41" coveredconditionals="0" methods="9" coveredmethods="0"/>
        <line num="43" count="1" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="86" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="97" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="99" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="111" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="123" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="128" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="141" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="194" count="1" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="198" count="1" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="202" count="1" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
      </file>
      <file name="Card.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\ui\Card.tsx">
        <metrics statements="21" coveredstatements="5" conditionals="4" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="39" count="1" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="110" count="1" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="148" count="1" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
      </file>
      <file name="FadeTransition.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\ui\FadeTransition.tsx">
        <metrics statements="5" coveredstatements="5" conditionals="5" coveredconditionals="5" methods="2" coveredmethods="2"/>
        <line num="18" count="1" type="stmt"/>
        <line num="25" count="42" type="cond" truecount="2" falsecount="0"/>
        <line num="27" count="42" type="stmt"/>
        <line num="28" count="42" type="stmt"/>
        <line num="35" count="42" type="stmt"/>
      </file>
      <file name="Input.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\ui\Input.tsx">
        <metrics statements="23" coveredstatements="3" conditionals="55" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="36" count="1" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="76" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="109" count="0" type="stmt"/>
        <line num="181" count="1" type="stmt"/>
        <line num="183" count="1" type="stmt"/>
      </file>
      <file name="LoadingAnimation.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\ui\LoadingAnimation.tsx">
        <metrics statements="38" coveredstatements="1" conditionals="17" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="25" count="1" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="58" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="80" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="169" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
      </file>
      <file name="Modal.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\ui\Modal.tsx">
        <metrics statements="28" coveredstatements="6" conditionals="23" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="59" count="1" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="82" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="112" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="176" count="1" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="213" count="1" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="228" count="1" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="251" count="1" type="stmt"/>
      </file>
      <file name="ModernInput.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\ui\ModernInput.tsx">
        <metrics statements="55" coveredstatements="1" conditionals="59" coveredconditionals="0" methods="11" coveredmethods="0"/>
        <line num="31" count="1" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="53" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="61" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="108" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="129" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="132" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="140" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="152" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="168" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
      </file>
      <file name="Progress.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\ui\Progress.tsx">
        <metrics statements="14" coveredstatements="3" conditionals="6" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="27" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="128" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
      </file>
      <file name="SafeAreaErrorBoundary.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\ui\SafeAreaErrorBoundary.tsx">
        <metrics statements="15" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
      </file>
      <file name="SafeAreaScreen.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\ui\SafeAreaScreen.tsx">
        <metrics statements="10" coveredstatements="1" conditionals="16" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="23" count="1" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="48" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
      </file>
      <file name="SafeAreaViewWrapper.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\ui\SafeAreaViewWrapper.tsx">
        <metrics statements="22" coveredstatements="0" conditionals="51" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="46" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="70" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
      </file>
      <file name="SafeScreenWrapper.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\ui\SafeScreenWrapper.tsx">
        <metrics statements="13" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="48" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
      </file>
      <file name="Skeleton.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\ui\Skeleton.tsx">
        <metrics statements="20" coveredstatements="6" conditionals="21" coveredconditionals="0" methods="9" coveredmethods="0"/>
        <line num="26" count="1" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="128" count="1" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="147" count="1" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="167" count="1" type="stmt"/>
      </file>
      <file name="Switch.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\ui\Switch.tsx">
        <metrics statements="16" coveredstatements="3" conditionals="17" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="26" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
      </file>
      <file name="Text.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\ui\Text.tsx">
        <metrics statements="6" coveredstatements="1" conditionals="4" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="26" count="1" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
      </file>
      <file name="Toast.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\ui\Toast.tsx">
        <metrics statements="36" coveredstatements="4" conditionals="29" coveredconditionals="0" methods="11" coveredmethods="0"/>
        <line num="45" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="109" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="124" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="208" count="1" type="stmt"/>
        <line num="213" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="215" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="232" count="1" type="stmt"/>
      </file>
      <file name="Typography.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\ui\Typography.tsx">
        <metrics statements="42" coveredstatements="30" conditionals="27" coveredconditionals="15" methods="13" coveredmethods="8"/>
        <line num="40" count="1" type="stmt"/>
        <line num="48" count="850" type="stmt"/>
        <line num="51" count="850" type="stmt"/>
        <line num="52" count="850" type="cond" truecount="6" falsecount="3"/>
        <line num="56" count="88" type="stmt"/>
        <line num="63" count="762" type="stmt"/>
        <line num="68" count="850" type="stmt"/>
        <line num="69" count="850" type="cond" truecount="5" falsecount="4"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="75" count="88" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="83" count="762" type="stmt"/>
        <line num="88" count="850" type="stmt"/>
        <line num="89" count="850" type="cond" truecount="3" falsecount="4"/>
        <line num="91" count="381" type="stmt"/>
        <line num="93" count="88" type="stmt"/>
        <line num="95" count="381" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="108" count="850" type="stmt"/>
        <line num="109" count="850" type="stmt"/>
        <line num="119" count="850" type="stmt"/>
        <line num="131" count="1" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="140" count="88" type="stmt"/>
        <line num="143" count="1" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="147" count="1" type="stmt"/>
        <line num="148" count="381" type="stmt"/>
        <line num="151" count="1" type="stmt"/>
        <line num="152" count="381" type="stmt"/>
        <line num="155" count="1" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="159" count="1" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="164" count="1" type="stmt"/>
      </file>
      <file name="index.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\components\ui\index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="config">
      <metrics statements="22" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="10" coveredmethods="0"/>
      <file name="testAccounts.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\config\testAccounts.ts">
        <metrics statements="22" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="20" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="236" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
      </file>
    </package>
    <package name="contexts">
      <metrics statements="174" coveredstatements="10" conditionals="59" coveredconditionals="4" methods="28" coveredmethods="1"/>
      <file name="AuthContext.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\contexts\AuthContext.tsx">
        <metrics statements="63" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="39" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="169" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
      </file>
      <file name="NavigationContext.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\contexts\NavigationContext.tsx">
        <metrics statements="39" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="16" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="84" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
      </file>
      <file name="ThemeContext.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\contexts\ThemeContext.tsx">
        <metrics statements="72" coveredstatements="10" conditionals="45" coveredconditionals="4" methods="10" coveredmethods="1"/>
        <line num="19" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="84" count="1" type="stmt"/>
        <line num="85" count="913" type="stmt"/>
        <line num="88" count="913" type="cond" truecount="1" falsecount="1"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="94" count="913" type="cond" truecount="3" falsecount="1"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="99" count="913" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="115" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="123" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="138" count="1" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="144" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="159" count="1" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="162" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="168" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="169" count="0" type="stmt"/>
        <line num="173" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="174" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
      </file>
    </package>
    <package name="hooks">
      <metrics statements="215" coveredstatements="0" conditionals="89" coveredconditionals="0" methods="49" coveredmethods="0"/>
      <file name="useEnhancedAuth.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\hooks\useEnhancedAuth.ts">
        <metrics statements="78" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="24" coveredmethods="0"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
      </file>
      <file name="useErrorHandler.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\hooks\useErrorHandler.ts">
        <metrics statements="76" coveredstatements="0" conditionals="47" coveredconditionals="0" methods="13" coveredmethods="0"/>
        <line num="48" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="69" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="96" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="101" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="106" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="135" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="136" count="0" type="stmt"/>
        <line num="140" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="141" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="163" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="164" count="0" type="stmt"/>
        <line num="168" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="169" count="0" type="stmt"/>
        <line num="173" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="174" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="200" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="229" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="231" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="232" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="237" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="238" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="246" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="247" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="255" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="256" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="273" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="275" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="276" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
      </file>
      <file name="useToast.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\hooks\useToast.ts">
        <metrics statements="61" coveredstatements="0" conditionals="36" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="22" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="60" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="81" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="88" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="89" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="110" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="138" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="139" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="146" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="147" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="158" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="186" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="189" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="204" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="207" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="222" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="225" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="240" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="243" count="0" type="stmt"/>
        <line num="249" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="250" count="0" type="stmt"/>
      </file>
    </package>
    <package name="lib">
      <metrics statements="28" coveredstatements="6" conditionals="7" coveredconditionals="0" methods="15" coveredmethods="1"/>
      <file name="utils.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\lib\utils.ts">
        <metrics statements="28" coveredstatements="6" conditionals="7" coveredconditionals="0" methods="15" coveredmethods="1"/>
        <line num="11" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="31" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="38" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="1" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="109" count="1" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="129" count="1" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="161" count="1" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
      </file>
    </package>
    <package name="navigation">
      <metrics statements="80" coveredstatements="0" conditionals="42" coveredconditionals="0" methods="14" coveredmethods="0"/>
      <file name="AppNavigator.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\navigation\AppNavigator.tsx">
        <metrics statements="18" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="60" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
      </file>
      <file name="AuthNavigator.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\navigation\AuthNavigator.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
      </file>
      <file name="MainNavigator.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\navigation\MainNavigator.tsx">
        <metrics statements="30" coveredstatements="0" conditionals="20" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="72" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="122" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="127" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
      </file>
      <file name="ProviderNavigator.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\navigation\ProviderNavigator.tsx">
        <metrics statements="29" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="77" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\navigation\index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="screens">
      <metrics statements="167" coveredstatements="0" conditionals="52" coveredconditionals="0" methods="54" coveredmethods="0"/>
      <file name="CategoryScreen.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\screens\CategoryScreen.tsx">
        <metrics statements="52" coveredstatements="0" conditionals="14" coveredconditionals="0" methods="15" coveredmethods="0"/>
        <line num="18" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="132" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="179" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="236" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="238" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="260" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="261" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
      </file>
      <file name="LoadingScreen.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\screens\LoadingScreen.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
      </file>
      <file name="ServiceDetailsScreen.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\screens\ServiceDetailsScreen.tsx">
        <metrics statements="56" coveredstatements="0" conditionals="34" coveredconditionals="0" methods="17" coveredmethods="0"/>
        <line num="16" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="129" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="207" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="274" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="276" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="334" count="0" type="stmt"/>
        <line num="335" count="0" type="stmt"/>
        <line num="364" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="365" count="0" type="stmt"/>
        <line num="374" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="375" count="0" type="stmt"/>
        <line num="384" count="0" type="stmt"/>
        <line num="405" count="0" type="stmt"/>
      </file>
      <file name="ServicesScreen.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\screens\ServicesScreen.tsx">
        <metrics statements="56" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="21" coveredmethods="0"/>
        <line num="19" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="187" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="238" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="240" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\screens\index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="screens.auth">
      <metrics statements="124" coveredstatements="0" conditionals="50" coveredconditionals="0" methods="43" coveredmethods="0"/>
      <file name="LoginScreen.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\screens\auth\LoginScreen.tsx">
        <metrics statements="46" coveredstatements="0" conditionals="20" coveredconditionals="0" methods="14" coveredmethods="0"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="72" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="97" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="105" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
      </file>
      <file name="RegisterScreen.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\screens\auth\RegisterScreen.tsx">
        <metrics statements="78" coveredstatements="0" conditionals="30" coveredconditionals="0" methods="29" coveredmethods="0"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="95" count="0" type="stmt"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="101" count="0" type="stmt"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="105" count="0" type="stmt"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="109" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="115" count="0" type="stmt"/>
        <line num="118" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="121" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="130" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\screens\auth\index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="screens.main">
      <metrics statements="141" coveredstatements="38" conditionals="122" coveredconditionals="35" methods="28" coveredmethods="3"/>
      <file name="BookingsScreen.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\screens\main\BookingsScreen.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
      </file>
      <file name="HomeScreen.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\screens\main\HomeScreen.tsx">
        <metrics statements="8" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
      </file>
      <file name="ProfileScreen.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\screens\main\ProfileScreen.tsx">
        <metrics statements="105" coveredstatements="38" conditionals="111" coveredconditionals="35" methods="12" coveredmethods="3"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="44" type="stmt"/>
        <line num="35" count="44" type="stmt"/>
        <line num="36" count="44" type="stmt"/>
        <line num="37" count="44" type="stmt"/>
        <line num="38" count="44" type="stmt"/>
        <line num="39" count="44" type="stmt"/>
        <line num="40" count="44" type="stmt"/>
        <line num="41" count="44" type="stmt"/>
        <line num="42" count="44" type="stmt"/>
        <line num="43" count="44" type="stmt"/>
        <line num="45" count="44" type="stmt"/>
        <line num="46" count="22" type="stmt"/>
        <line num="49" count="44" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="55" count="44" type="stmt"/>
        <line num="56" count="22" type="stmt"/>
        <line num="57" count="22" type="stmt"/>
        <line num="58" count="22" type="stmt"/>
        <line num="61" count="22" type="stmt"/>
        <line num="64" count="21" type="cond" truecount="1" falsecount="1"/>
        <line num="85" count="21" type="stmt"/>
        <line num="86" count="21" type="stmt"/>
        <line num="87" count="21" type="stmt"/>
        <line num="105" count="1" type="stmt"/>
        <line num="106" count="1" type="stmt"/>
        <line num="108" count="22" type="stmt"/>
        <line num="112" count="44" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="120" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="123" count="0" type="stmt"/>
        <line num="125" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="126" count="0" type="stmt"/>
        <line num="128" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="129" count="0" type="stmt"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="132" count="0" type="stmt"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="135" count="0" type="stmt"/>
        <line num="138" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="174" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="175" count="0" type="stmt"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="181" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="211" count="44" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="215" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="216" count="0" type="stmt"/>
        <line num="236" count="44" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="240" count="44" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="255" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="256" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="269" count="44" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="277" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="278" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="291" count="44" type="cond" truecount="2" falsecount="0"/>
        <line num="292" count="22" type="stmt"/>
        <line num="302" count="22" type="cond" truecount="2" falsecount="0"/>
        <line num="303" count="1" type="stmt"/>
        <line num="315" count="21" type="cond" truecount="3" falsecount="1"/>
        <line num="316" count="0" type="stmt"/>
        <line num="319" count="21" type="stmt"/>
        <line num="338" count="0" type="stmt"/>
        <line num="406" count="0" type="stmt"/>
        <line num="432" count="1" type="stmt"/>
      </file>
      <file name="ServicesScreen.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\screens\main\ServicesScreen.tsx">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
      </file>
      <file name="SettingsScreen.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\screens\main\SettingsScreen.tsx">
        <metrics statements="22" coveredstatements="0" conditionals="11" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="73" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\screens\main\index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="screens.provider">
      <metrics statements="399" coveredstatements="0" conditionals="207" coveredconditionals="0" methods="118" coveredmethods="0"/>
      <file name="AddServiceScreen.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\screens\provider\AddServiceScreen.tsx">
        <metrics statements="39" coveredstatements="0" conditionals="10" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="97" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="98" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
      </file>
      <file name="EditServiceScreen.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\screens\provider\EditServiceScreen.tsx">
        <metrics statements="65" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="16" coveredmethods="0"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="97" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="171" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="172" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="182" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="183" count="0" type="stmt"/>
        <line num="186" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="187" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
      </file>
      <file name="EnhancedAddServiceScreen.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\screens\provider\EnhancedAddServiceScreen.tsx">
        <metrics statements="60" coveredstatements="0" conditionals="25" coveredconditionals="0" methods="19" coveredmethods="0"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="112" count="0" type="stmt"/>
        <line num="115" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="120" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="132" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="166" count="0" type="stmt"/>
        <line num="173" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
      </file>
      <file name="EnhancedProviderServicesScreen.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\screens\provider\EnhancedProviderServicesScreen.tsx">
        <metrics statements="107" coveredstatements="0" conditionals="71" coveredconditionals="0" methods="30" coveredmethods="0"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="78" count="0" type="stmt"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="81" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="88" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="108" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="115" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="123" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="124" count="0" type="stmt"/>
        <line num="128" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="132" count="0" type="stmt"/>
        <line num="138" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="140" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="176" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="212" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="227" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="256" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="257" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="353" count="0" type="stmt"/>
      </file>
      <file name="ProviderDashboardScreen.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\screens\provider\ProviderDashboardScreen.tsx">
        <metrics statements="44" coveredstatements="0" conditionals="30" coveredconditionals="0" methods="15" coveredmethods="0"/>
        <line num="20" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="203" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="204" count="0" type="stmt"/>
        <line num="207" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="208" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="223" count="0" type="stmt"/>
        <line num="379" count="0" type="stmt"/>
        <line num="384" count="0" type="stmt"/>
        <line num="401" count="0" type="stmt"/>
        <line num="405" count="0" type="stmt"/>
        <line num="430" count="0" type="stmt"/>
      </file>
      <file name="ProviderServicesScreen.tsx" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\screens\provider\ProviderServicesScreen.tsx">
        <metrics statements="84" coveredstatements="0" conditionals="55" coveredconditionals="0" methods="30" coveredmethods="0"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="127" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="146" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="148" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="179" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="185" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="349" count="0" type="stmt"/>
        <line num="355" count="0" type="stmt"/>
        <line num="361" count="0" type="stmt"/>
        <line num="371" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="372" count="0" type="stmt"/>
        <line num="375" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
        <line num="405" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\screens\provider\index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="services">
      <metrics statements="98" coveredstatements="0" conditionals="45" coveredconditionals="0" methods="16" coveredmethods="0"/>
      <file name="authService.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\services\authService.ts">
        <metrics statements="89" coveredstatements="0" conditionals="41" coveredconditionals="0" methods="14" coveredmethods="0"/>
        <line num="37" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="71" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="77" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="91" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="191" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="199" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="226" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="227" count="0" type="stmt"/>
        <line num="231" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="232" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="242" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="307" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="309" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="322" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="323" count="0" type="stmt"/>
        <line num="326" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="327" count="0" type="stmt"/>
        <line num="330" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="331" count="0" type="stmt"/>
        <line num="334" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="335" count="0" type="stmt"/>
        <line num="338" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="339" count="0" type="stmt"/>
        <line num="342" count="0" type="stmt"/>
        <line num="347" count="0" type="stmt"/>
      </file>
      <file name="authServiceEpic01.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\services\authServiceEpic01.ts">
        <metrics statements="9" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
      </file>
    </package>
    <package name="services.api">
      <metrics statements="116" coveredstatements="5" conditionals="38" coveredconditionals="1" methods="39" coveredmethods="0"/>
      <file name="auth.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\services\api\auth.ts">
        <metrics statements="23" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
      </file>
      <file name="client.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\services\api\client.ts">
        <metrics statements="33" coveredstatements="4" conditionals="20" coveredconditionals="1" methods="4" coveredmethods="0"/>
        <line num="10" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="15" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="29" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="78" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="92" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\services\api\index.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
      <file name="profile.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\services\api\profile.ts">
        <metrics statements="43" coveredstatements="1" conditionals="17" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="117" count="1" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="179" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="214" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="245" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="246" count="0" type="stmt"/>
        <line num="251" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="252" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
      </file>
      <file name="provider.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\services\api\provider.ts">
        <metrics statements="17" coveredstatements="0" conditionals="1" coveredconditionals="0" methods="16" coveredmethods="0"/>
        <line num="114" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
      </file>
    </package>
    <package name="theme">
      <metrics statements="6" coveredstatements="6" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="index.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\theme\index.ts">
        <metrics statements="6" coveredstatements="6" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="7" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
        <line num="144" count="1" type="stmt"/>
        <line num="203" count="1" type="stmt"/>
      </file>
    </package>
    <package name="utils">
      <metrics statements="481" coveredstatements="0" conditionals="320" coveredconditionals="0" methods="83" coveredmethods="0"/>
      <file name="accessibility.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\utils\accessibility.ts">
        <metrics statements="42" coveredstatements="0" conditionals="44" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="7" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="123" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="125" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="127" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="133" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="148" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
      </file>
      <file name="authUtils.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\utils\authUtils.ts">
        <metrics statements="111" coveredstatements="0" conditionals="56" coveredconditionals="0" methods="17" coveredmethods="0"/>
        <line num="14" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="26" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="32" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="33" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="135" count="0" type="stmt"/>
        <line num="138" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="139" count="0" type="stmt"/>
        <line num="143" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="144" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="156" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="176" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="177" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="179" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="187" count="0" type="stmt"/>
        <line num="190" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="191" count="0" type="stmt"/>
        <line num="194" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="195" count="0" type="stmt"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="199" count="0" type="stmt"/>
        <line num="202" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="203" count="0" type="stmt"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="207" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="219" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="226" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="227" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="241" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="242" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="258" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="259" count="0" type="stmt"/>
        <line num="264" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="272" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="273" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="296" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="297" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
      </file>
      <file name="errorHandler.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\utils\errorHandler.ts">
        <metrics statements="60" coveredstatements="0" conditionals="52" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="31" count="0" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="34" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="44" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="80" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="97" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="98" count="0" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="103" count="0" type="stmt"/>
        <line num="110" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="112" count="0" type="stmt"/>
        <line num="114" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="115" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="158" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="188" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="189" count="0" type="stmt"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="195" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="198" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="221" count="0" type="stmt"/>
        <line num="222" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="228" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
      </file>
      <file name="errorTypes.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\utils\errorTypes.ts">
        <metrics statements="22" coveredstatements="0" conditionals="26" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="117" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="196" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="197" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="199" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="216" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="217" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="218" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="223" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="225" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="229" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="231" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
      </file>
      <file name="errorUtils.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\utils\errorUtils.ts">
        <metrics statements="103" coveredstatements="0" conditionals="79" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="30" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="70" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="88" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="109" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="123" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="167" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="171" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="189" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="190" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="203" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
        <line num="272" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="273" count="0" type="stmt"/>
        <line num="279" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="280" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="323" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="325" count="0" type="stmt"/>
        <line num="327" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="328" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
      </file>
      <file name="networkConfig.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\utils\networkConfig.ts">
        <metrics statements="49" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="11" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="93" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="141" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="142" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="163" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="164" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
      </file>
      <file name="responsiveUtils.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\utils\responsiveUtils.ts">
        <metrics statements="34" coveredstatements="0" conditionals="15" coveredconditionals="0" methods="10" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="36" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="97" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="120" count="0" type="stmt"/>
      </file>
      <file name="safeAreaPatch.ts" path="C:\Users\<USER>\Desktop\Files\Services_Startup\Workspace\services-app\code\frontend\src\utils\safeAreaPatch.ts">
        <metrics statements="60" coveredstatements="0" conditionals="32" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="31" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="64" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="77" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
