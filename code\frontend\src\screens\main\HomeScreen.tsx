/**
 * Home Screen
 * Main dashboard for authenticated users
 */

import React from 'react';
import { View, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Text, Button } from '../../components';
import { useAuth } from '../../contexts/AuthContext';

interface HomeScreenProps {
  navigation: any;
}

export const HomeScreen: React.FC<HomeScreenProps> = ({ navigation }) => {
  const { logout } = useAuth();

  const handleLogout = async () => {
    try {
      // Use AuthContext logout instead of direct navigation reset
      await logout();
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text variant="heading1" align="center" style={styles.title}>
          Welcome to Vierla
        </Text>
        <Text variant="body" color="secondary" align="center" style={styles.subtitle}>
          Your beauty and wellness services marketplace
        </Text>
        
        <View style={styles.actions}>
          <Button
            title="Logout"
            onPress={handleLogout}
            variant="outline"
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  title: {
    marginBottom: 8,
  },
  subtitle: {
    marginBottom: 32,
  },
  actions: {
    width: '100%',
    maxWidth: 300,
  },
});
