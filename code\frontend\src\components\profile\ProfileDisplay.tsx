/**
 * ProfileDisplay Component
 *
 * Displays user profile information in a read-only format
 * Following the "digital sanctuary" design philosophy with proper typography and spacing
 */

import React from 'react';
import {
  View,
  StyleSheet,
} from 'react-native';
import { User, UserProfile } from '../../services/api/profile';
import { useTheme } from '../../contexts/ThemeContext';
import {
  Headline2,
  BodyText,
  SubtitleText,
  CaptionText
} from '../ui/Typography';

interface ProfileDisplayProps {
  user: User;
  profile: UserProfile | null;
}

export const ProfileDisplay: React.FC<ProfileDisplayProps> = ({ user, profile }) => {
  const { colors, borderRadius, shadows, spacing } = useTheme();

  // Create styles using theme
  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    section: {
      backgroundColor: colors.background.secondary, // Warm Cream background
      marginBottom: spacing.md, // 16pt spacing
      borderRadius: borderRadius.lg, // 12pt corner radius as per design system
      padding: spacing.md, // 16pt internal padding
      ...shadows.level1, // Level 1 elevation shadow for cards
    },
    infoItem: {
      marginBottom: spacing.sm, // 8pt spacing between items
    },
    infoPlaceholder: {
      color: colors.extended.sageVariant, // Mid-gray-green for placeholder text
      fontStyle: 'italic',
    },
  });

  const InfoSection: React.FC<{
    title: string;
    children: React.ReactNode;
  }> = ({ title, children }) => (
    <View style={styles.section}>
      <Headline2 color="secondary" style={{ marginBottom: spacing.md }}>
        {title}
      </Headline2>
      {children}
    </View>
  );

  const InfoItem: React.FC<{
    label: string;
    value: string | null | undefined;
    placeholder?: string;
    testID?: string;
  }> = ({ label, value, placeholder = 'Not provided', testID }) => (
    <View style={styles.infoItem} testID={testID}>
      <SubtitleText color="tertiary" style={{ marginBottom: 4 }}>
        {label}
      </SubtitleText>
      <BodyText style={!value ? styles.infoPlaceholder : undefined}>
        {value || placeholder}
      </BodyText>
    </View>
  );

  return (
    <View style={styles.container} testID="profile-display">
      {/* Personal Information */}
      <InfoSection title="Personal Information">
        <InfoItem label="Full Name" value={user.full_name} testID="profile-display-name" />
        <InfoItem label="Email Address" value={user.email} testID="profile-display-email" />
        <InfoItem label="Phone Number" value={user.phone} />
        <InfoItem label="Date of Birth" value={user.date_of_birth} />
        {user.bio && <InfoItem label="Bio" value={user.bio} />}
      </InfoSection>

      {/* Location Information */}
      {profile && (profile.address || profile.city || profile.state) && (
        <InfoSection title="Location">
          <InfoItem label="Address" value={profile.address} />
          <InfoItem label="City" value={profile.city} />
          <InfoItem label="State/Province" value={profile.state} />
          <InfoItem label="Postal Code" value={profile.zip_code} />
          <InfoItem label="Country" value={profile.country} />
        </InfoSection>
      )}

      {/* Business Information - Only for service providers */}
      {user.role === 'service_provider' && profile && (
        <InfoSection title="Business Information">
          <InfoItem label="Business Name" value={profile.business_name} />
          <InfoItem label="Business Description" value={profile.business_description} />
          <InfoItem label="Years of Experience" value={profile.years_of_experience?.toString()} />
          <InfoItem label="Website" value={profile.website} />
          <InfoItem label="Instagram" value={profile.instagram} />
          <InfoItem label="Facebook" value={profile.facebook} />
        </InfoSection>
      )}

      {/* Account Information */}
      <InfoSection title="Account Details">
        <InfoItem
          label="Account Type"
          value={user.role === 'customer' ? 'Customer' : 'Service Provider'}
        />
        <InfoItem
          label="Account Status"
          value={user.account_status === 'active' ? 'Active' : user.account_status}
        />
        <InfoItem
          label="Email Verified"
          value={user.is_verified ? 'Verified' : 'Not Verified'}
        />
        <InfoItem
          label="Member Since"
          value={user.created_at ? new Date(user.created_at).toLocaleDateString() : undefined}
        />
      </InfoSection>

      {/* Privacy Preferences - Only show if user has preferences */}
      {profile && (profile.show_phone_publicly !== undefined || profile.show_email_publicly !== undefined) && (
        <InfoSection title="Privacy Settings">
          <InfoItem
            label="Show Phone Publicly"
            value={profile.show_phone_publicly ? 'Yes' : 'No'}
          />
          <InfoItem
            label="Show Email Publicly"
            value={profile.show_email_publicly ? 'Yes' : 'No'}
          />
          <InfoItem
            label="Allow Reviews"
            value={profile.allow_reviews ? 'Yes' : 'No'}
          />
        </InfoSection>
      )}
    </View>
  );
};

export default ProfileDisplay;
