/**
 * Enhanced Profile Components Test Suite
 * Comprehensive tests for profile components following TDD principles
 * Tests written before implementation to ensure proper functionality
 */

import React from 'react';
import { render, fireEvent, waitFor, screen } from '@testing-library/react-native';
import { Alert } from 'react-native';
import { ProfileDisplay } from '../ProfileDisplay';
import { ProfileForm } from '../ProfileForm';
import { AvatarUpload } from '../AvatarUpload';
import { User, UserProfile } from '../../../services/api/profile';

// Mock Alert
jest.spyOn(Alert, 'alert');

// Mock expo-haptics
jest.mock('expo-haptics', () => ({
  impactAsync: jest.fn(),
  ImpactFeedbackStyle: {
    Light: 'light',
    Medium: 'medium',
    Heavy: 'heavy',
  },
}));

// Mock expo-image-picker
jest.mock('expo-image-picker', () => ({
  requestCameraPermissionsAsync: jest.fn(),
  requestMediaLibraryPermissionsAsync: jest.fn(),
  launchCameraAsync: jest.fn(),
  launchImageLibraryAsync: jest.fn(),
  MediaTypeOptions: {
    Images: 'Images',
  },
}));

// Mock theme context with complete theme object
jest.mock('../../../contexts/ThemeContext', () => ({
  useTheme: () => ({
    colors: {
      primary: '#D81B60',
      background: {
        primary: '#F5F5F5',
        secondary: '#FFFFFF',
        tertiary: '#E0E0E0',
      },
      text: {
        primary: '#212121',
        secondary: '#616161',
        tertiary: '#9E9E9E',
      },
      border: '#E0E0E0',
      error: '#C62828',
      success: '#2E7D32',
      extended: {
        sageVariant: '#8BC34A',
      },
    },
    spacing: {
      xs: 4,
      sm: 8,
      md: 16,
      lg: 24,
      xl: 32,
    },
    borderRadius: {
      sm: 4,
      md: 8,
      lg: 12,
      xl: 16,
    },
    shadows: {
      level1: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 1,
      },
    },
  }),
}));

// Mock UI components
jest.mock('../../ui/Typography', () => ({
  Headline2: ({ children, style, ...props }: any) => (
    <div style={style} {...props}>{children}</div>
  ),
  BodyText: ({ children, style, ...props }: any) => (
    <div style={style} {...props}>{children}</div>
  ),
  SubtitleText: ({ children, style, ...props }: any) => (
    <div style={style} {...props}>{children}</div>
  ),
  CaptionText: ({ children, style, ...props }: any) => (
    <div style={style} {...props}>{children}</div>
  ),
  ButtonText: ({ children, style, ...props }: any) => (
    <div style={style} {...props}>{children}</div>
  ),
}));

jest.mock('../../ui/Button', () => ({
  PrimaryButton: ({ children, onPress, disabled, testID, ...props }: any) => (
    <button onClick={onPress} disabled={disabled} data-testid={testID} {...props}>
      {children}
    </button>
  ),
  SecondaryButton: ({ children, onPress, disabled, testID, ...props }: any) => (
    <button onClick={onPress} disabled={disabled} data-testid={testID} {...props}>
      {children}
    </button>
  ),
  TertiaryButton: ({ children, onPress, disabled, testID, ...props }: any) => (
    <button onClick={onPress} disabled={disabled} data-testid={testID} {...props}>
      {children}
    </button>
  ),
}));

jest.mock('../../ui/ModernInput', () => ({
  ModernInput: ({ label, value, onChangeText, error, testID, required, ...props }: any) => (
    <div>
      <label>{label}{required && ' *'}</label>
      <input
        value={value}
        onChange={(e) => onChangeText?.(e.target.value)}
        data-testid={testID}
        {...props}
      />
      {error && <div data-testid={`${testID}-error`}>{error}</div>}
    </div>
  ),
}));

// Mock data
const mockUser: User = {
  id: '1',
  email: '<EMAIL>',
  username: 'testuser',
  first_name: 'Test',
  last_name: 'User',
  full_name: 'Test User',
  phone: '+**********',
  role: 'customer',
  avatar: 'https://example.com/avatar.jpg',
  date_of_birth: '1990-01-01',
  bio: 'Test bio',
  account_status: 'active',
  is_verified: true,
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z',
};

const mockProfile: UserProfile = {
  address: '123 Main St',
  city: 'Toronto',
  state: 'ON',
  zip_code: 'M5V 3A8',
  country: 'Canada',
  latitude: 43.6532,
  longitude: -79.3832,
  full_address: '123 Main St, Toronto, ON, M5V 3A8, Canada',
  has_location: true,
  business_name: 'Test Business',
  business_description: 'Test business description',
  years_of_experience: 5,
  website: 'https://testbusiness.com',
  instagram: '@testbusiness',
  facebook: 'testbusiness',
  search_radius: 25,
  auto_accept_bookings: false,
  show_phone_publicly: true,
  show_email_publicly: false,
  allow_reviews: true,
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z',
};

describe('Enhanced Profile Components', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('ProfileDisplay Component', () => {
    it('should render user information correctly', () => {
      render(<ProfileDisplay user={mockUser} profile={mockProfile} />);

      expect(screen.getByText('Test User')).toBeTruthy();
      expect(screen.getByText('<EMAIL>')).toBeTruthy();
      expect(screen.getByText('+**********')).toBeTruthy();
      expect(screen.getByText('Test bio')).toBeTruthy();
    });

    it('should render profile information correctly', () => {
      render(<ProfileDisplay user={mockUser} profile={mockProfile} />);

      expect(screen.getByText('123 Main St')).toBeTruthy();
      expect(screen.getByText('Toronto')).toBeTruthy();
      expect(screen.getByText('Test Business')).toBeTruthy();
      expect(screen.getByText('Test business description')).toBeTruthy();
    });

    it('should handle missing profile data gracefully', () => {
      const emptyProfile: UserProfile = {};
      render(<ProfileDisplay user={mockUser} profile={emptyProfile} />);

      expect(screen.getByText('Test User')).toBeTruthy();
      expect(screen.getByText('<EMAIL>')).toBeTruthy();
    });

    it('should handle missing user data gracefully', () => {
      const incompleteUser = { ...mockUser, phone: undefined, bio: undefined };
      render(<ProfileDisplay user={incompleteUser} profile={mockProfile} />);

      expect(screen.getByText('Test User')).toBeTruthy();
      expect(screen.getByText('<EMAIL>')).toBeTruthy();
    });
  });

  describe('ProfileForm Component', () => {
    const mockFormData = {
      first_name: 'Test',
      last_name: 'User',
      phone: '+**********',
      bio: 'Test bio',
      address: '123 Main St',
      city: 'Toronto',
      state: 'ON',
      zip_code: 'M5V 3A8',
      country: 'Canada',
    };

    const mockSetFormData = jest.fn();
    const mockOnSave = jest.fn();
    const mockOnCancel = jest.fn();

    it('should render form fields correctly', () => {
      render(
        <ProfileForm
          user={mockUser}
          profile={mockProfile}
          formData={mockFormData}
          setFormData={mockSetFormData}
          errors={{}}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      expect(screen.getByTestId('first-name-input')).toBeTruthy();
      expect(screen.getByTestId('last-name-input')).toBeTruthy();
      expect(screen.getByTestId('phone-input')).toBeTruthy();
      expect(screen.getByTestId('bio-input')).toBeTruthy();
    });

    it('should handle form input changes', () => {
      render(
        <ProfileForm
          user={mockUser}
          profile={mockProfile}
          formData={mockFormData}
          setFormData={mockSetFormData}
          errors={{}}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      const firstNameInput = screen.getByTestId('first-name-input');
      fireEvent.changeText(firstNameInput, 'Updated Name');

      expect(mockSetFormData).toHaveBeenCalled();
    });

    it('should display validation errors', () => {
      const errors = {
        first_name: 'First name is required',
        phone: 'Invalid phone number',
      };

      render(
        <ProfileForm
          user={mockUser}
          profile={mockProfile}
          formData={mockFormData}
          setFormData={mockSetFormData}
          errors={errors}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      expect(screen.getByTestId('first-name-input-error')).toBeTruthy();
      expect(screen.getByTestId('phone-input-error')).toBeTruthy();
      expect(screen.getByText('First name is required')).toBeTruthy();
      expect(screen.getByText('Invalid phone number')).toBeTruthy();
    });

    it('should handle save and cancel actions', () => {
      render(
        <ProfileForm
          user={mockUser}
          profile={mockProfile}
          formData={mockFormData}
          setFormData={mockSetFormData}
          errors={{}}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      );

      const saveButton = screen.getByTestId('save-button');
      const cancelButton = screen.getByTestId('cancel-button');

      fireEvent.press(saveButton);
      expect(mockOnSave).toHaveBeenCalled();

      fireEvent.press(cancelButton);
      expect(mockOnCancel).toHaveBeenCalled();
    });
  });

  describe('AvatarUpload Component', () => {
    const mockOnUpload = jest.fn();
    const mockOnRemove = jest.fn();
    const mockOnClose = jest.fn();

    it('should render avatar upload modal when visible', () => {
      render(
        <AvatarUpload
          currentAvatar="https://example.com/avatar.jpg"
          onUpload={mockOnUpload}
          onRemove={mockOnRemove}
          isVisible={true}
          onClose={mockOnClose}
        />
      );

      expect(screen.getByTestId('take-photo-button')).toBeTruthy();
      expect(screen.getByTestId('choose-gallery-button')).toBeTruthy();
      expect(screen.getByTestId('remove-photo-button')).toBeTruthy();
    });

    it('should not render when not visible', () => {
      render(
        <AvatarUpload
          currentAvatar="https://example.com/avatar.jpg"
          onUpload={mockOnUpload}
          onRemove={mockOnRemove}
          isVisible={false}
          onClose={mockOnClose}
        />
      );

      expect(screen.queryByTestId('take-photo-button')).toBeNull();
    });

    it('should handle upload button interactions', () => {
      render(
        <AvatarUpload
          currentAvatar="https://example.com/avatar.jpg"
          onUpload={mockOnUpload}
          onRemove={mockOnRemove}
          isVisible={true}
          onClose={mockOnClose}
        />
      );

      const takePhotoButton = screen.getByTestId('take-photo-button');
      const chooseGalleryButton = screen.getByTestId('choose-gallery-button');

      fireEvent.press(takePhotoButton);
      fireEvent.press(chooseGalleryButton);

      // These will trigger permission requests and image picker
      // Actual implementation will be tested in integration tests
    });

    it('should handle remove photo action', () => {
      render(
        <AvatarUpload
          currentAvatar="https://example.com/avatar.jpg"
          onUpload={mockOnUpload}
          onRemove={mockOnRemove}
          isVisible={true}
          onClose={mockOnClose}
        />
      );

      const removeButton = screen.getByTestId('remove-photo-button');
      fireEvent.press(removeButton);

      expect(mockOnRemove).toHaveBeenCalled();
    });

    it('should not show remove button when no avatar exists', () => {
      render(
        <AvatarUpload
          currentAvatar={undefined}
          onUpload={mockOnUpload}
          onRemove={mockOnRemove}
          isVisible={true}
          onClose={mockOnClose}
        />
      );

      expect(screen.queryByTestId('remove-photo-button')).toBeNull();
    });
  });
});
