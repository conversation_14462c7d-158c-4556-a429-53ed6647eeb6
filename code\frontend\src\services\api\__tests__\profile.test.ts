/**
 * Profile API Service Test Suite
 * Comprehensive tests for all profile-related API calls
 * Updated to match current implementation and test missing functionality
 */

import { profileAP<PERSON>, ProfileUpdateRequest, ProfileDetailsUpdateRequest, ImageAsset, APIError } from '../profile';

// Mock the API client
jest.mock('../client', () => ({
  apiClient: {
    get: jest.fn(),
    patch: jest.fn(),
    post: jest.fn(),
    delete: jest.fn(),
  },
}));

import { apiClient } from '../client';
const mockedApiClient = apiClient as jest.Mocked<typeof apiClient>;

// Mock console methods to avoid noise in tests
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeAll(() => {
  console.error = jest.fn();
  console.warn = jest.fn();
});

afterAll(() => {
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});

describe('Profile API Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getProfile', () => {
    it('should make GET request to profile endpoint', async () => {
      const mockResponse = {
        data: {
          id: '1',
          email: '<EMAIL>',
          username: 'testuser',
          first_name: 'Test',
          last_name: 'User',
          full_name: 'Test User',
          phone: '+**********',
          role: 'customer',
          avatar: 'https://example.com/avatar.jpg',
          date_of_birth: '1990-01-01',
          bio: 'Test bio',
          account_status: 'active',
          is_verified: true,
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z',
        },
      };

      mockedApiClient.get.mockResolvedValue(mockResponse);

      const result = await profileAPI.getProfile();

      expect(mockedApiClient.get).toHaveBeenCalledWith('/auth/profile/');
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle network errors', async () => {
      const networkError = {
        request: {},
        message: 'Network error. Please check your internet connection.',
      };

      mockedApiClient.get.mockRejectedValue(networkError);

      await expect(profileAPI.getProfile()).rejects.toMatchObject({
        message: 'Network error - please check your connection',
        status: 0,
      });
    });

    it('should handle server errors', async () => {
      const serverError = {
        response: {
          status: 500,
          data: { error: 'Internal server error' },
        },
      };

      mockedApiClient.get.mockRejectedValue(serverError);

      await expect(profileAPI.getProfile()).rejects.toMatchObject({
        message: 'An error occurred',
        status: 500,
        field_errors: { error: 'Internal server error' },
      });
    });
  });

  describe('updateProfile', () => {
    it('should make PATCH request to profile update endpoint', async () => {
      const updateData: ProfileUpdateRequest = {
        first_name: 'Updated',
        last_name: 'Name',
        phone: '+**********',
        bio: 'Updated bio',
      };

      const mockResponse = {
        data: {
          id: '1',
          email: '<EMAIL>',
          username: 'testuser',
          first_name: 'Updated',
          last_name: 'Name',
          full_name: 'Updated Name',
          phone: '+**********',
          role: 'customer',
          bio: 'Updated bio',
          account_status: 'active',
          is_verified: true,
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T12:00:00Z',
        },
      };

      mockedApiClient.patch.mockResolvedValue(mockResponse);

      const result = await profileAPI.updateProfile(updateData);

      expect(mockedApiClient.patch).toHaveBeenCalledWith('/auth/profile/update/', updateData);
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle validation errors', async () => {
      const updateData: ProfileUpdateRequest = {
        first_name: '',
        phone: 'invalid-phone',
      };

      const validationError = {
        response: {
          status: 400,
          data: {
            first_name: ['This field is required.'],
            phone: ['Enter a valid phone number.'],
          },
        },
      };

      mockedApiClient.patch.mockRejectedValue(validationError);

      await expect(profileAPI.updateProfile(updateData)).rejects.toMatchObject({
        message: 'An error occurred',
        status: 400,
        field_errors: {
          first_name: ['This field is required.'],
          phone: ['Enter a valid phone number.'],
        },
      });
    });

    it('should handle authentication errors', async () => {
      const updateData: ProfileUpdateRequest = {
        first_name: 'Updated',
      };

      const authError = {
        response: {
          status: 401,
          data: { detail: 'Authentication credentials were not provided.' },
        },
      };

      mockedApiClient.patch.mockRejectedValue(authError);

      await expect(profileAPI.updateProfile(updateData)).rejects.toMatchObject({
        message: 'Authentication credentials were not provided.',
        status: 401,
        field_errors: { detail: 'Authentication credentials were not provided.' },
      });
    });
  });

  describe('getProfileDetails', () => {
    it('should make GET request to profile details endpoint', async () => {
      const mockResponse = {
        data: {
          address: '123 Main St',
          city: 'Vancouver',
          state: 'BC',
          zip_code: 'V6B 1A1',
          country: 'Canada',
          business_name: 'Test Business',
          business_description: 'Test Description',
          years_of_experience: 5,
          website: 'https://example.com',
          instagram: '@testuser',
          facebook: 'testuser',
          search_radius: 25,
          auto_accept_bookings: false,
          show_phone_publicly: true,
          show_email_publicly: false,
          allow_reviews: true,
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z',
        },
      };

      mockedApiClient.get.mockResolvedValue(mockResponse);

      const result = await profileAPI.getProfileDetails();

      expect(mockedApiClient.get).toHaveBeenCalledWith('/auth/profile/details/');
      expect(result).toEqual(mockResponse.data);
    });

    it('should return null and log warning when endpoint returns 404', async () => {
      const notFoundError = {
        response: {
          status: 404,
          data: { detail: 'Not found' },
        },
      };

      mockedApiClient.get.mockRejectedValue(notFoundError);

      const result = await profileAPI.getProfileDetails();

      expect(result).toBeNull();
      expect(console.warn).toHaveBeenCalledWith('getProfileDetails: Profile details endpoint not found, returning null');
    });

    it('should handle other errors by throwing them', async () => {
      const serverError = {
        response: {
          status: 500,
          data: { error: 'Internal server error' },
        },
      };

      mockedApiClient.get.mockRejectedValue(serverError);

      await expect(profileAPI.getProfileDetails()).rejects.toEqual(serverError);
      expect(console.error).toHaveBeenCalledWith('Get profile details error:', serverError);
    });
  });

  describe('updateProfileDetails', () => {
    it('should make PATCH request to profile details endpoint', async () => {
      const updateData: ProfileDetailsUpdateRequest = {
        address: '456 Oak St',
        city: 'Vancouver',
        business_name: 'Updated Business',
        years_of_experience: 10,
      };

      const mockResponse = {
        data: {
          address: '456 Oak St',
          city: 'Vancouver',
          business_name: 'Updated Business',
          years_of_experience: 10,
          updated_at: '2023-01-01T12:00:00Z',
        },
      };

      mockedApiClient.patch.mockResolvedValue(mockResponse);

      const result = await profileAPI.updateProfileDetails(updateData);

      expect(mockedApiClient.patch).toHaveBeenCalledWith('/auth/profile/details/', updateData);
      expect(result).toEqual(mockResponse.data);
    });

    it('should fall back to main profile endpoint when details endpoint returns 404', async () => {
      const updateData: ProfileDetailsUpdateRequest = {
        business_name: 'Test Business',
      };

      const notFoundError = {
        response: {
          status: 404,
          data: { detail: 'Not found' },
        },
      };

      const mockProfileResponse = {
        data: {
          id: '1',
          first_name: 'Test Business',
          updated_at: '2023-01-01T12:00:00Z',
        },
      };

      mockedApiClient.patch
        .mockRejectedValueOnce(notFoundError)
        .mockResolvedValueOnce(mockProfileResponse);

      const result = await profileAPI.updateProfileDetails(updateData);

      expect(console.warn).toHaveBeenCalledWith('updateProfileDetails: Profile details endpoint not found, using main profile endpoint');
      expect(mockedApiClient.patch).toHaveBeenCalledWith('/auth/profile/update/', { first_name: 'Test Business' });
      expect(result).toBeNull();
    });

    it('should handle empty update data gracefully', async () => {
      const updateData: ProfileDetailsUpdateRequest = {};

      const notFoundError = {
        response: {
          status: 404,
          data: { detail: 'Not found' },
        },
      };

      mockedApiClient.patch.mockRejectedValue(notFoundError);

      const result = await profileAPI.updateProfileDetails(updateData);

      expect(result).toBeNull();
      expect(console.warn).toHaveBeenCalledWith('updateProfileDetails: Profile details endpoint not found, using main profile endpoint');
    });

    it('should handle other errors by throwing them', async () => {
      const updateData: ProfileDetailsUpdateRequest = {
        business_name: 'Test Business',
      };

      const serverError = {
        response: {
          status: 500,
          data: { error: 'Internal server error' },
        },
      };

      mockedApiClient.patch.mockRejectedValue(serverError);

      await expect(profileAPI.updateProfileDetails(updateData)).rejects.toEqual(serverError);
      expect(console.error).toHaveBeenCalledWith('Update profile details error:', serverError);
    });
  });

  describe('uploadAvatar', () => {
    it('should use profile update endpoint with FormData', async () => {
      const mockImageAsset: ImageAsset = {
        uri: 'file://test-avatar.jpg',
        type: 'image/jpeg',
        name: 'avatar.jpg',
      };

      const mockResponse = {
        data: {
          avatar: 'https://example.com/new-avatar.jpg',
        },
      };

      mockedApiClient.patch.mockResolvedValue(mockResponse);

      const result = await profileAPI.uploadAvatar(mockImageAsset);

      expect(mockedApiClient.patch).toHaveBeenCalledWith(
        '/auth/profile/update/',
        expect.any(FormData),
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      expect(result).toEqual('https://example.com/new-avatar.jpg');
    });

    it('should return original URI when avatar not in response', async () => {
      const mockImageAsset: ImageAsset = {
        uri: 'file://test-avatar.jpg',
        type: 'image/jpeg',
        name: 'avatar.jpg',
      };

      const mockResponse = {
        data: {
          id: '1',
          first_name: 'Test',
          // No avatar field in response
        },
      };

      mockedApiClient.patch.mockResolvedValue(mockResponse);

      const result = await profileAPI.uploadAvatar(mockImageAsset);

      expect(result).toEqual('file://test-avatar.jpg');
    });

    it('should handle upload errors', async () => {
      const mockImageAsset: ImageAsset = {
        uri: 'file://test-avatar.jpg',
        type: 'image/jpeg',
        name: 'avatar.jpg',
      };

      const uploadError = new Error('Upload failed');
      mockedApiClient.patch.mockRejectedValue(uploadError);

      await expect(profileAPI.uploadAvatar(mockImageAsset)).rejects.toMatchObject({
        message: 'Upload failed',
      });
      expect(console.error).toHaveBeenCalledWith('Upload avatar error:', uploadError);
    });
  });

  describe('deleteAvatar', () => {
    it('should use profile update endpoint to clear avatar', async () => {
      const mockResponse = {
        data: {
          id: '1',
          avatar: null,
        },
      };

      mockedApiClient.patch.mockResolvedValue(mockResponse);

      await profileAPI.deleteAvatar();

      expect(mockedApiClient.patch).toHaveBeenCalledWith('/auth/profile/update/', { avatar: null });
    });

    it('should handle delete errors', async () => {
      const deleteError = new Error('Delete failed');
      mockedApiClient.patch.mockRejectedValue(deleteError);

      await expect(profileAPI.deleteAvatar()).rejects.toMatchObject({
        message: 'Delete failed',
      });
      expect(console.error).toHaveBeenCalledWith('Delete avatar error:', deleteError);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle timeout errors', async () => {
      const timeoutError = {
        code: 'ECONNABORTED',
        message: 'timeout of 5000ms exceeded',
      };

      mockedApiClient.get.mockRejectedValue(timeoutError);

      await expect(profileAPI.getProfile()).rejects.toMatchObject({
        message: 'timeout of 5000ms exceeded',
      });
    });

    it('should handle malformed response data', async () => {
      const malformedResponse = {
        data: null,
      };

      mockedApiClient.get.mockResolvedValue(malformedResponse);

      const result = await profileAPI.getProfile();
      expect(result).toBeNull();
    });

    it('should handle empty update data', async () => {
      const emptyData: ProfileUpdateRequest = {};

      const mockResponse = {
        data: {
          id: '1',
          email: '<EMAIL>',
          username: 'testuser',
          first_name: 'Test',
          last_name: 'User',
          full_name: 'Test User',
          phone: '+**********',
          role: 'customer',
          account_status: 'active',
          is_verified: true,
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z',
        },
      };

      mockedApiClient.patch.mockResolvedValue(mockResponse);

      const result = await profileAPI.updateProfile(emptyData);

      expect(mockedApiClient.patch).toHaveBeenCalledWith('/auth/profile/update/', emptyData);
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle concurrent update requests', async () => {
      const updateData1: ProfileUpdateRequest = { first_name: 'First' };
      const updateData2: ProfileUpdateRequest = { last_name: 'Second' };

      const mockResponse1 = {
        data: { id: '1', first_name: 'First', updated_at: '2023-01-01T12:00:00Z' },
      };
      const mockResponse2 = {
        data: { id: '1', last_name: 'Second', updated_at: '2023-01-01T12:01:00Z' },
      };

      mockedApiClient.patch
        .mockResolvedValueOnce(mockResponse1)
        .mockResolvedValueOnce(mockResponse2);

      const [result1, result2] = await Promise.all([
        profileAPI.updateProfile(updateData1),
        profileAPI.updateProfile(updateData2),
      ]);

      expect(result1.first_name).toBe('First');
      expect(result2.last_name).toBe('Second');
    });
  });

  describe('Data Validation and Sanitization', () => {
    it('should handle special characters in profile data', async () => {
      const updateData: ProfileUpdateRequest = {
        first_name: 'José María',
        last_name: 'García-López',
        bio: 'Bio with émojis 🎉 and special chars: <script>alert("test")</script>',
      };

      const mockResponse = {
        data: {
          id: '1',
          first_name: 'José María',
          last_name: 'García-López',
          bio: 'Bio with émojis 🎉 and special chars: <script>alert("test")</script>',
          updated_at: '2023-01-01T12:00:00Z',
        },
      };

      mockedApiClient.patch.mockResolvedValue(mockResponse);

      const result = await profileAPI.updateProfile(updateData);

      expect(result.first_name).toBe('José María');
      expect(result.last_name).toBe('García-López');
      expect(result.bio).toContain('émojis 🎉');
    });

    it('should handle very long bio text', async () => {
      const longBio = 'A'.repeat(1000);
      const updateData: ProfileUpdateRequest = {
        bio: longBio,
      };

      const validationError = {
        response: {
          status: 400,
          data: {
            bio: ['Ensure this field has no more than 500 characters.'],
          },
        },
      };

      mockedApiClient.patch.mockRejectedValue(validationError);

      await expect(profileAPI.updateProfile(updateData)).rejects.toMatchObject({
        message: 'An error occurred',
        status: 400,
        field_errors: {
          bio: ['Ensure this field has no more than 500 characters.'],
        },
      });
    });
  });

  describe('Error Handler', () => {
    it('should format response errors correctly', () => {
      const responseError = {
        response: {
          status: 400,
          data: {
            detail: 'Validation failed',
            errors: {
              first_name: ['This field is required.'],
            },
          },
        },
      };

      const formattedError = profileAPI.handleError(responseError);

      expect(formattedError).toEqual({
        message: 'Validation failed',
        status: 400,
        field_errors: {
          first_name: ['This field is required.'],
        },
      });
    });

    it('should format network errors correctly', () => {
      const networkError = {
        request: {},
        message: 'Network Error',
      };

      const formattedError = profileAPI.handleError(networkError);

      expect(formattedError).toEqual({
        message: 'Network error - please check your connection',
        status: 0,
      });
    });

    it('should format generic errors correctly', () => {
      const genericError = {
        message: 'Something went wrong',
      };

      const formattedError = profileAPI.handleError(genericError);

      expect(formattedError).toEqual({
        message: 'Something went wrong',
      });
    });

    it('should handle errors without message', () => {
      const errorWithoutMessage = {};

      const formattedError = profileAPI.handleError(errorWithoutMessage);

      expect(formattedError).toEqual({
        message: 'An unexpected error occurred',
      });
    });
  });

  describe('validateProfileData', () => {
    it('should validate phone number format', () => {
      const validData: ProfileUpdateRequest = {
        phone: '+**********',
      };
      const invalidData: ProfileUpdateRequest = {
        phone: '**********',
      };

      const validResult = profileAPI.validateProfileData(validData);
      const invalidResult = profileAPI.validateProfileData(invalidData);

      expect(validResult.isValid).toBe(true);
      expect(validResult.errors).toHaveLength(0);

      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors).toContain('Phone number must include country code (e.g., +**********)');
    });

    it('should validate bio length', () => {
      const validData: ProfileUpdateRequest = {
        bio: 'A'.repeat(500),
      };
      const invalidData: ProfileUpdateRequest = {
        bio: 'A'.repeat(501),
      };

      const validResult = profileAPI.validateProfileData(validData);
      const invalidResult = profileAPI.validateProfileData(invalidData);

      expect(validResult.isValid).toBe(true);
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors).toContain('Bio cannot exceed 500 characters');
    });

    it('should validate date of birth format', () => {
      const validData: ProfileUpdateRequest = {
        date_of_birth: '1990-01-01',
      };
      const invalidData: ProfileUpdateRequest = {
        date_of_birth: '01/01/1990',
      };

      const validResult = profileAPI.validateProfileData(validData);
      const invalidResult = profileAPI.validateProfileData(invalidData);

      expect(validResult.isValid).toBe(true);
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors).toContain('Date of birth must be in YYYY-MM-DD format');
    });
  });

  describe('transformUserData', () => {
    it('should transform user data with display name and avatar status', () => {
      const user = {
        id: '1',
        email: '<EMAIL>',
        username: 'testuser',
        first_name: 'John',
        last_name: 'Doe',
        full_name: 'John Doe',
        avatar: 'https://example.com/avatar.jpg',
      } as any;

      const transformed = profileAPI.transformUserData(user);

      expect(transformed.displayName).toBe('John Doe');
      expect(transformed.hasAvatar).toBe(true);
    });

    it('should handle missing full_name', () => {
      const user = {
        id: '1',
        email: '<EMAIL>',
        username: 'testuser',
        first_name: 'John',
        last_name: 'Doe',
        full_name: '',
        avatar: null,
      } as any;

      const transformed = profileAPI.transformUserData(user);

      expect(transformed.displayName).toBe('John Doe');
      expect(transformed.hasAvatar).toBe(false);
    });
  });

  describe('isProfileComplete', () => {
    it('should return true for complete profile', () => {
      const user = {
        first_name: 'John',
        last_name: 'Doe',
        email: '<EMAIL>',
      } as any;

      const isComplete = profileAPI.isProfileComplete(user);
      expect(isComplete).toBe(true);
    });

    it('should return false for incomplete profile', () => {
      const user = {
        first_name: 'John',
        last_name: '',
        email: '<EMAIL>',
      } as any;

      const isComplete = profileAPI.isProfileComplete(user);
      expect(isComplete).toBe(false);
    });
  });

  describe('Integration Tests', () => {
    it('should handle complete profile update workflow', async () => {
      // First get profile
      const getResponse = {
        data: {
          id: '1',
          first_name: 'John',
          last_name: 'Doe',
          email: '<EMAIL>',
        },
      };
      mockedApiClient.get.mockResolvedValueOnce(getResponse);

      // Then update profile
      const updateData: ProfileUpdateRequest = {
        first_name: 'Jane',
        bio: 'Updated bio',
      };
      const updateResponse = {
        data: {
          ...getResponse.data,
          ...updateData,
          updated_at: '2023-01-01T12:00:00Z',
        },
      };
      mockedApiClient.patch.mockResolvedValueOnce(updateResponse);

      const profile = await profileAPI.getProfile();
      const updatedProfile = await profileAPI.updateProfile(updateData);

      expect(profile.first_name).toBe('John');
      expect(updatedProfile.first_name).toBe('Jane');
      expect(updatedProfile.bio).toBe('Updated bio');
    });

    it('should handle avatar upload workflow', async () => {
      const imageAsset: ImageAsset = {
        uri: 'file://avatar.jpg',
        type: 'image/jpeg',
        name: 'avatar.jpg',
      };

      const uploadResponse = {
        data: {
          avatar: 'https://example.com/avatar.jpg',
        },
      };
      mockedApiClient.patch.mockResolvedValueOnce(uploadResponse);

      const avatarUrl = await profileAPI.uploadAvatar(imageAsset);

      expect(avatarUrl).toBe('https://example.com/avatar.jpg');
    });
  });
});
