/**
 * ModernInput Component
 * Implements modern form design patterns from the UI/UX design document
 * Features floating labels, proper states, and accessibility compliance
 */

import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  TextInput,
  StyleSheet,
  Animated,
  TextInputProps,
  ViewStyle,
} from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { BodyText, CaptionText } from './Typography';

// Input state types
export type InputState = 'default' | 'focused' | 'filled' | 'error' | 'disabled';

interface ModernInputProps extends Omit<TextInputProps, 'style'> {
  label: string;
  error?: string;
  helperText?: string;
  containerStyle?: ViewStyle;
  required?: boolean;
  disabled?: boolean;
}

export const ModernInput: React.FC<ModernInputProps> = ({
  label,
  error,
  helperText,
  value = '',
  onChangeText,
  onFocus,
  onBlur,
  containerStyle,
  required = false,
  disabled = false,
  multiline = false,
  numberOfLines = 1,
  testID,
  ...textInputProps
}) => {
  const { colors, typography, spacing, borderRadius } = useTheme();
  const [isFocused, setIsFocused] = useState(false);
  const [hasValue, setHasValue] = useState(!!value);
  
  // Animated values for floating label
  const labelPosition = useRef(new Animated.Value(hasValue || isFocused ? 1 : 0)).current;
  const borderColor = useRef(new Animated.Value(0)).current;

  // Update hasValue when value prop changes
  useEffect(() => {
    setHasValue(!!value);
    if (!!value && !isFocused) {
      animateLabelUp();
    } else if (!value && !isFocused) {
      animateLabelDown();
    }
  }, [value, isFocused]);

  // Animate label up (floating state)
  const animateLabelUp = () => {
    Animated.parallel([
      Animated.timing(labelPosition, {
        toValue: 1,
        duration: 200,
        useNativeDriver: false,
      }),
      Animated.timing(borderColor, {
        toValue: 1,
        duration: 200,
        useNativeDriver: false,
      }),
    ]).start();
  };

  // Animate label down (placeholder state)
  const animateLabelDown = () => {
    Animated.parallel([
      Animated.timing(labelPosition, {
        toValue: 0,
        duration: 200,
        useNativeDriver: false,
      }),
      Animated.timing(borderColor, {
        toValue: 0,
        duration: 200,
        useNativeDriver: false,
      }),
    ]).start();
  };

  // Handle focus
  const handleFocus = (e: any) => {
    setIsFocused(true);
    animateLabelUp();
    onFocus?.(e);
  };

  // Handle blur
  const handleBlur = (e: any) => {
    setIsFocused(false);
    if (!hasValue) {
      animateLabelDown();
    } else {
      // Keep border color animation for filled state
      Animated.timing(borderColor, {
        toValue: 0,
        duration: 200,
        useNativeDriver: false,
      }).start();
    }
    onBlur?.(e);
  };

  // Handle text change
  const handleChangeText = (text: string) => {
    setHasValue(!!text);
    onChangeText?.(text);
  };

  // Get current input state
  const getCurrentState = (): InputState => {
    if (disabled) return 'disabled';
    if (error) return 'error';
    if (isFocused) return 'focused';
    if (hasValue) return 'filled';
    return 'default';
  };

  const currentState = getCurrentState();

  // Get border color based on state
  const getBorderColor = () => {
    const focusedColor = error ? colors.error : colors.primary; // Forest Green or error
    const defaultColor = colors.extended.sageVariant; // Mid-gray-green
    
    return borderColor.interpolate({
      inputRange: [0, 1],
      outputRange: [defaultColor, focusedColor],
    });
  };

  // Get label color based on state
  const getLabelColor = () => {
    switch (currentState) {
      case 'error':
        return colors.error;
      case 'focused':
        return colors.primary; // Forest Green
      case 'disabled':
        return colors.extended.sageVariant;
      case 'filled':
        return colors.text.tertiary; // Sage Green
      default:
        return colors.text.tertiary; // Sage Green
    }
  };

  // Get text color based on state
  const getTextColor = () => {
    switch (currentState) {
      case 'disabled':
        return colors.extended.sageVariant;
      default:
        return colors.text.primary; // Deep Charcoal
    }
  };

  // Create styles
  const styles = StyleSheet.create({
    container: {
      marginBottom: spacing.md,
    },
    inputContainer: {
      position: 'relative',
      borderRadius: borderRadius.sm, // 4pt radius for inputs
      backgroundColor: colors.background.secondary, // Warm Cream background
      minHeight: multiline ? 80 : 56, // Minimum height for touch targets
    },
    input: {
      fontFamily: typography.fontFamily.body, // Inter font
      fontSize: typography.fontSize.body, // 16pt
      lineHeight: typography.lineHeight.body, // 24pt
      color: getTextColor(),
      paddingHorizontal: spacing.md, // 16pt horizontal padding
      paddingTop: multiline ? spacing.lg : spacing.md + 8, // Extra top padding for floating label
      paddingBottom: multiline ? spacing.md : spacing.sm, // 8pt bottom padding
      textAlignVertical: multiline ? 'top' : 'center',
      minHeight: multiline ? 80 : 56,
    },
    labelContainer: {
      position: 'absolute',
      left: spacing.md,
      zIndex: 1,
      backgroundColor: colors.background.secondary, // Match input background
      paddingHorizontal: 4,
    },
    label: {
      color: getLabelColor(),
    },
    border: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderRadius: borderRadius.sm,
      borderWidth: 1,
    },
    helperContainer: {
      marginTop: spacing.xs, // 4pt spacing
      paddingHorizontal: spacing.md,
    },
    helperText: {
      color: colors.text.tertiary, // Sage Green
    },
    errorText: {
      color: colors.error,
    },
  });

  // Calculate label position and size
  const labelTop = labelPosition.interpolate({
    inputRange: [0, 1],
    outputRange: [multiline ? 24 : 18, -8], // Move to top border when floating
  });

  const labelScale = labelPosition.interpolate({
    inputRange: [0, 1],
    outputRange: [1, 0.85], // Scale down when floating
  });

  return (
    <View style={[styles.container, containerStyle]} testID={testID}>
      <View style={styles.inputContainer}>
        {/* Animated border */}
        <Animated.View
          style={[
            styles.border,
            {
              borderColor: getBorderColor(),
              borderWidth: currentState === 'focused' ? 2 : 1,
            },
          ]}
        />

        {/* Floating label */}
        <Animated.View
          style={[
            styles.labelContainer,
            {
              top: labelTop,
              transform: [{ scale: labelScale }],
            },
          ]}
        >
          <CaptionText style={styles.label}>
            {label}{required && ' *'}
          </CaptionText>
        </Animated.View>

        {/* Text input */}
        <TextInput
          style={styles.input}
          value={value}
          onChangeText={handleChangeText}
          onFocus={handleFocus}
          onBlur={handleBlur}
          editable={!disabled}
          multiline={multiline}
          numberOfLines={numberOfLines}
          placeholderTextColor={colors.extended.sageVariant}
          selectionColor={colors.primary}
          testID={testID}
          accessibilityLabel={label}
          accessibilityHint={helperText}
          accessibilityDescribedBy={error ? `${testID}-error` : undefined}
          {...textInputProps}
        />
      </View>

      {/* Helper text or error */}
      {(helperText || error) && (
        <View style={styles.helperContainer}>
          <CaptionText
            style={error ? styles.errorText : styles.helperText}
            testID={error ? `${testID}-error` : `${testID}-helper`}
          >
            {error || helperText}
          </CaptionText>
        </View>
      )}
    </View>
  );
};

export default ModernInput;
