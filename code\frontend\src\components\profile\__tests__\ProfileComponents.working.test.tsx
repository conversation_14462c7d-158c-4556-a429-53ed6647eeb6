/**
 * Profile Components Test Suite - Working Implementation
 * Tests for ProfileDisplay, ProfileForm, and AvatarUpload components
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react-native';
import { Alert } from 'react-native';

// Mock dependencies
jest.mock('../../ui/SafeAreaViewWrapper', () => ({
  SafeAreaViewWrapper: ({ children, testID }: any) => <div testID={testID}>{children}</div>,
}));

jest.mock('../../ui/AnimatedCard', () => ({
  AnimatedCard: ({ children, testID }: any) => <div testID={testID}>{children}</div>,
}));

jest.mock('../../ui/FadeTransition', () => ({
  FadeTransition: ({ children, visible, testID }: any) => 
    visible ? <div testID={testID}>{children}</div> : null,
}));

jest.mock('../../ui/LoadingAnimation', () => ({
  LoadingAnimation: ({ testID }: any) => <div testID={testID}>Loading...</div>,
}));

// Mock React Native components
jest.mock('react-native', () => ({
  ...jest.requireActual('react-native'),
  Alert: {
    alert: jest.fn(),
  },
  Dimensions: {
    get: () => ({ width: 375, height: 812 }),
  },
}));

// Import components after mocks
import { ProfileDisplay } from '../ProfileDisplay';
import { ProfileForm } from '../ProfileForm';
import { AvatarUpload } from '../AvatarUpload';

// Mock data
const mockUser = {
  id: '1',
  email: '<EMAIL>',
  username: 'testuser',
  first_name: 'John',
  last_name: 'Doe',
  full_name: 'John Doe',
  phone: '+**********',
  role: 'customer' as const,
  avatar: 'https://example.com/avatar.jpg',
  date_of_birth: '1990-01-01',
  bio: 'Test bio',
  account_status: 'active' as const,
  is_verified: true,
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z',
};

const mockProfile = {
  address: '123 Main St',
  city: 'Toronto',
  state: 'ON',
  zip_code: 'M5V 3A8',
  country: 'Canada',
  latitude: 43.6532,
  longitude: -79.3832,
  business_name: 'Test Business',
  business_description: 'Professional services',
  years_of_experience: 5,
  website: 'https://testbusiness.com',
  instagram: '@testbusiness',
  facebook: 'testbusiness',
  search_radius: 25,
  auto_accept_bookings: false,
  show_phone_publicly: true,
  show_email_publicly: false,
  allow_reviews: true,
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z',
};

describe('ProfileDisplay Component', () => {
  it('should render user information correctly', () => {
    render(<ProfileDisplay user={mockUser} profile={mockProfile} />);

    expect(screen.getByText('John Doe')).toBeTruthy();
    expect(screen.getByText('<EMAIL>')).toBeTruthy();
    expect(screen.getByText('+**********')).toBeTruthy();
    expect(screen.getByText('Test bio')).toBeTruthy();
  });

  it('should display location information', () => {
    render(<ProfileDisplay user={mockUser} profile={mockProfile} />);

    expect(screen.getByText('123 Main St')).toBeTruthy();
    expect(screen.getByText('Toronto')).toBeTruthy();
    expect(screen.getByText('ON')).toBeTruthy();
    expect(screen.getByText('Canada')).toBeTruthy();
  });

  it('should show business information for providers', () => {
    const providerUser = { ...mockUser, role: 'service_provider' as const };
    render(<ProfileDisplay user={providerUser} profile={mockProfile} />);

    expect(screen.getByText('Test Business')).toBeTruthy();
    expect(screen.getByText('Professional services')).toBeTruthy();
    expect(screen.getByText('5')).toBeTruthy();
    expect(screen.getByText('https://testbusiness.com')).toBeTruthy();
  });

  it('should handle missing profile data gracefully', () => {
    render(<ProfileDisplay user={mockUser} profile={null} />);

    expect(screen.getByText('John Doe')).toBeTruthy();
    expect(screen.getByText('<EMAIL>')).toBeTruthy();
    // Should not crash when profile is null
  });
});

describe('ProfileForm Component', () => {
  const mockFormData = {
    first_name: 'John',
    last_name: 'Doe',
    phone: '+**********',
    bio: 'Test bio',
    date_of_birth: '1990-01-01',
  };

  const mockSetFormData = jest.fn();
  const mockOnSave = jest.fn();
  const mockOnCancel = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render form fields correctly', () => {
    render(
      <ProfileForm
        user={mockUser}
        profile={mockProfile}
        formData={mockFormData}
        setFormData={mockSetFormData}
        errors={{}}
        onSave={mockOnSave}
        onCancel={mockOnCancel}
        isLoading={false}
      />
    );

    expect(screen.getByDisplayValue('John')).toBeTruthy();
    expect(screen.getByDisplayValue('Doe')).toBeTruthy();
    expect(screen.getByDisplayValue('+**********')).toBeTruthy();
    expect(screen.getByDisplayValue('Test bio')).toBeTruthy();
  });

  it('should call setFormData when input changes', () => {
    render(
      <ProfileForm
        user={mockUser}
        profile={mockProfile}
        formData={mockFormData}
        setFormData={mockSetFormData}
        errors={{}}
        onSave={mockOnSave}
        onCancel={mockOnCancel}
        isLoading={false}
      />
    );

    const firstNameInput = screen.getByDisplayValue('John');
    fireEvent.changeText(firstNameInput, 'Jane');

    expect(mockSetFormData).toHaveBeenCalledWith(
      expect.objectContaining({
        first_name: 'Jane',
      })
    );
  });

  it('should display validation errors', () => {
    const errors = {
      first_name: 'First name is required',
      phone: 'Invalid phone number',
    };

    render(
      <ProfileForm
        user={mockUser}
        profile={mockProfile}
        formData={mockFormData}
        setFormData={mockSetFormData}
        errors={errors}
        onSave={mockOnSave}
        onCancel={mockOnCancel}
        isLoading={false}
      />
    );

    expect(screen.getByText('First name is required')).toBeTruthy();
    expect(screen.getByText('Invalid phone number')).toBeTruthy();
  });

  it('should call onSave when save button is pressed', () => {
    render(
      <ProfileForm
        user={mockUser}
        profile={mockProfile}
        formData={mockFormData}
        setFormData={mockSetFormData}
        errors={{}}
        onSave={mockOnSave}
        onCancel={mockOnCancel}
        isLoading={false}
      />
    );

    const saveButton = screen.getByText('Save Changes');
    fireEvent.press(saveButton);

    expect(mockOnSave).toHaveBeenCalled();
  });

  it('should call onCancel when cancel button is pressed', () => {
    render(
      <ProfileForm
        user={mockUser}
        profile={mockProfile}
        formData={mockFormData}
        setFormData={mockSetFormData}
        errors={{}}
        onSave={mockOnSave}
        onCancel={mockOnCancel}
        isLoading={false}
      />
    );

    const cancelButton = screen.getByText('Cancel');
    fireEvent.press(cancelButton);

    expect(mockOnCancel).toHaveBeenCalled();
  });

  it('should disable buttons when loading', () => {
    render(
      <ProfileForm
        user={mockUser}
        profile={mockProfile}
        formData={mockFormData}
        setFormData={mockSetFormData}
        errors={{}}
        onSave={mockOnSave}
        onCancel={mockOnCancel}
        isLoading={true}
      />
    );

    const saveButton = screen.getByText('Saving...');
    expect(saveButton.props.disabled).toBe(true);
  });
});

describe('AvatarUpload Component', () => {
  const mockOnUpload = jest.fn();
  const mockOnRemove = jest.fn();
  const mockOnClose = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render avatar upload modal when visible', () => {
    render(
      <AvatarUpload
        currentAvatar="https://example.com/avatar.jpg"
        onUpload={mockOnUpload}
        onRemove={mockOnRemove}
        isVisible={true}
        onClose={mockOnClose}
        isPremium={false}
      />
    );

    expect(screen.getByText('Update Profile Picture')).toBeTruthy();
  });

  it('should not render when not visible', () => {
    render(
      <AvatarUpload
        currentAvatar="https://example.com/avatar.jpg"
        onUpload={mockOnUpload}
        onRemove={mockOnRemove}
        isVisible={false}
        onClose={mockOnClose}
        isPremium={false}
      />
    );

    expect(screen.queryByText('Update Profile Picture')).toBeNull();
  });

  it('should call onClose when close button is pressed', () => {
    render(
      <AvatarUpload
        currentAvatar="https://example.com/avatar.jpg"
        onUpload={mockOnUpload}
        onRemove={mockOnRemove}
        isVisible={true}
        onClose={mockOnClose}
        isPremium={false}
      />
    );

    const closeButton = screen.getByText('Cancel');
    fireEvent.press(closeButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('should show premium features for premium users', () => {
    render(
      <AvatarUpload
        currentAvatar="https://example.com/avatar.jpg"
        onUpload={mockOnUpload}
        onRemove={mockOnRemove}
        isVisible={true}
        onClose={mockOnClose}
        isPremium={true}
      />
    );

    // Premium users should see additional options
    expect(screen.getByText('Choose from Gallery')).toBeTruthy();
    expect(screen.getByText('Take Photo')).toBeTruthy();
  });
});
