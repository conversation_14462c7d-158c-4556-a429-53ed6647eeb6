/**
 * AvatarUpload Component
 * Handles avatar upload and management functionality
 * Following the "digital sanctuary" design philosophy with sophisticated UI
 */

import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  TouchableOpacity,
  Modal,
  Image,
  StyleSheet,
  Alert,
  Animated,
  Dimensions,
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import * as ImageManipulator from 'expo-image-manipulator';
import { useTheme } from '../../contexts/ThemeContext';
import {
  Headline2,
  BodyText,
  CaptionText,
  ButtonText
} from '../ui/Typography';
import { PrimaryButton, SecondaryButton, TertiaryButton } from '../ui/Button';
import { profileAPI } from '../../services/api/profile';

interface AvatarUploadProps {
  currentAvatar?: string;
  onUpload: (imageUri: string) => Promise<void>;
  onRemove: () => Promise<void>;
  isVisible: boolean;
  onClose: () => void;
  isPremium?: boolean; // For Rich Gold accent features
  enableCompression?: boolean;
  maxImageSize?: number; // in MB
}

// Skeleton Loader Component
const SkeletonLoader: React.FC<{ style?: any }> = ({ style }) => {
  const { colors } = useTheme();
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: false,
        }),
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: false,
        }),
      ])
    );
    animation.start();
    return () => animation.stop();
  }, [animatedValue]);

  const backgroundColor = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [colors.extended.lightest, colors.extended.sageVariant],
  });

  return <Animated.View style={[style, { backgroundColor }]} />;
};

export const AvatarUpload: React.FC<AvatarUploadProps> = ({
  currentAvatar,
  onUpload,
  onRemove,
  isVisible,
  onClose,
  isPremium = false,
  enableCompression = true,
  maxImageSize = 5, // 5MB default
}) => {
  const { colors, borderRadius, shadows, spacing } = useTheme();
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [isCompressing, setIsCompressing] = useState(false);

  // Animation values
  const modalAnimation = useRef(new Animated.Value(0)).current;
  const scaleAnimation = useRef(new Animated.Value(0.8)).current;

  // Animate modal in/out
  useEffect(() => {
    if (isVisible) {
      Animated.parallel([
        Animated.timing(modalAnimation, {
          toValue: 1,
          duration: 300,
          useNativeDriver: false,
        }),
        Animated.spring(scaleAnimation, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(modalAnimation, {
          toValue: 0,
          duration: 200,
          useNativeDriver: false,
        }),
        Animated.timing(scaleAnimation, {
          toValue: 0.8,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [isVisible, modalAnimation, scaleAnimation]);

  const validateFile = (fileSize: number, fileType: string): boolean => {
    setError(null); // Clear previous errors

    // Check file size
    const maxSize = maxImageSize * 1024 * 1024; // Convert MB to bytes
    if (fileSize > maxSize) {
      const errorMessage = `Please select an image smaller than ${maxImageSize}MB.`;
      setError(errorMessage);
      return false;
    }

    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!allowedTypes.includes(fileType.toLowerCase())) {
      const errorMessage = 'Please select a valid image file (JPEG, PNG, or GIF).';
      setError(errorMessage);
      return false;
    }

    return true;
  };

  const compressImage = async (uri: string): Promise<string> => {
    if (!enableCompression) return uri;

    try {
      setIsCompressing(true);

      // Compress and resize image
      const manipulatedImage = await ImageManipulator.manipulateAsync(
        uri,
        [
          { resize: { width: 400, height: 400 } }, // Resize to 400x400 for avatars
        ],
        {
          compress: 0.8, // 80% quality
          format: ImageManipulator.SaveFormat.JPEG,
        }
      );

      return manipulatedImage.uri;
    } catch (error) {
      console.error('Image compression error:', error);
      // Return original URI if compression fails
      return uri;
    } finally {
      setIsCompressing(false);
    }
  };

  const simulateUploadProgress = () => {
    setUploadProgress(0);
    const interval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          return 100;
        }
        return prev + Math.random() * 15;
      });
    }, 100);
    return interval;
  };

  const handleImagePicker = async (useCamera: boolean) => {
    try {
      // Request permissions
      if (useCamera) {
        const { status } = await ImagePicker.requestCameraPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert(
            'Camera Permission',
            'Camera access is required to take photos. Please enable camera permission in settings.',
            [{ text: 'OK' }]
          );
          return;
        }
      } else {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert(
            'Photo Library Permission',
            'Photo library access is required to select images. Please enable photo library permission in settings.',
            [{ text: 'OK' }]
          );
          return;
        }
      }

      // Launch image picker
      const result = useCamera
        ? await ImagePicker.launchCameraAsync({
            mediaTypes: ImagePicker.MediaTypeOptions.Images,
            allowsEditing: true,
            aspect: [1, 1],
            quality: 0.8,
          })
        : await ImagePicker.launchImageLibraryAsync({
            mediaTypes: ImagePicker.MediaTypeOptions.Images,
            allowsEditing: true,
            aspect: [1, 1],
            quality: 0.8,
          });

      if (!result.canceled && result.assets && result.assets[0]) {
        const asset = result.assets[0];

        // Validate file size (approximate from dimensions)
        const estimatedSize = (asset.width || 800) * (asset.height || 800) * 3; // Rough estimate
        if (!validateFile(estimatedSize, 'image/jpeg')) {
          return;
        }

        setIsUploading(true);
        setError(null);

        try {
          // Compress image if enabled
          const finalUri = await compressImage(asset.uri);

          // Simulate upload progress
          const progressInterval = simulateUploadProgress();

          // Upload the image using the profile API
          await onUpload(finalUri);

          clearInterval(progressInterval);
          setUploadProgress(100);

          // Complete upload after a brief delay
          setTimeout(() => {
            setIsUploading(false);
            setUploadProgress(0);
            onClose();
          }, 300);
        } catch (uploadError) {
          console.error('Upload error:', uploadError);
          setError('Failed to upload image. Please try again.');
          setIsUploading(false);
          setUploadProgress(0);
        }
      }
    } catch (error) {
      console.error('Image picker error:', error);
      setError('Failed to select image. Please try again.');
      setIsUploading(false);
    }
  };

  const handleRemovePhoto = async () => {
    Alert.alert(
      'Remove Photo',
      'Are you sure you want to remove your profile picture?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              setIsUploading(true);
              setError(null);
              await onRemove();
              setIsUploading(false);
              onClose();
            } catch (error) {
              console.error('Remove avatar error:', error);
              setError('Failed to remove photo. Please try again.');
              setIsUploading(false);
            }
          }
        },
      ]
    );
  };

  // All styles are now inline for better theme integration

  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="none" // We handle animation manually
      onRequestClose={onClose}
    >
      <Animated.View style={{
        flex: 1,
        backgroundColor: modalAnimation.interpolate({
          inputRange: [0, 1],
          outputRange: ['rgba(0,0,0,0)', 'rgba(0,0,0,0.5)'],
        }),
        justifyContent: 'center',
        alignItems: 'center',
        padding: spacing.lg,
      }} testID="modal-backdrop">
        <TouchableOpacity
          style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0 }}
          onPress={onClose}
          testID="modal-backdrop"
        />
        <Animated.View
          style={{
            backgroundColor: colors.background.secondary, // Warm Cream
            borderRadius: borderRadius.lg, // 12pt corner radius
            padding: spacing.lg, // 24pt padding
            width: '100%',
            maxWidth: 400,
            ...shadows.level2, // Level 2 elevation for modals
            transform: [{ scale: scaleAnimation }],
          }}
          testID="avatar-upload-modal"
          accessibilityRole="dialog"
          accessibilityLabel="Change profile picture options"
        >
          {/* Header */}
          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: spacing.lg,
          }}>
            <Headline2 color="secondary">Change Profile Picture</Headline2>
            <TouchableOpacity
              style={{
                width: 32,
                height: 32,
                borderRadius: 16,
                backgroundColor: colors.extended.lightest,
                justifyContent: 'center',
                alignItems: 'center',
              }}
              onPress={onClose}
              testID="close-modal-button"
              accessibilityLabel="Close avatar upload modal"
            >
              <CaptionText style={{
                fontSize: 20,
                color: colors.text.tertiary,
                fontWeight: '300',
              }}>×</CaptionText>
            </TouchableOpacity>
          </View>

          {/* Current Avatar Preview */}
          <View style={{
            alignItems: 'center',
            marginBottom: spacing.lg,
          }}>
            <View style={{
              position: 'relative',
              width: 120,
              height: 120,
              borderRadius: 60,
              overflow: 'hidden',
              backgroundColor: colors.extended.lightest,
              ...shadows.level1,
            }}>
              {isUploading ? (
                <SkeletonLoader style={{
                  width: '100%',
                  height: '100%',
                }} />
              ) : currentAvatar ? (
                <Image
                  source={{ uri: currentAvatar }}
                  style={{
                    width: '100%',
                    height: '100%',
                  }}
                  testID="current-avatar"
                />
              ) : (
                <View style={{
                  width: '100%',
                  height: '100%',
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: colors.extended.nearWhite,
                }} testID="default-avatar">
                  <CaptionText style={{
                    color: colors.text.tertiary,
                    fontSize: 14,
                    fontWeight: '500',
                  }}>No Photo</CaptionText>
                </View>
              )}

              {/* Premium Badge */}
              {isPremium && (
                <View style={{
                  position: 'absolute',
                  top: -4,
                  right: -4,
                  backgroundColor: colors.accent, // Rich Gold
                  borderRadius: 12,
                  paddingHorizontal: 8,
                  paddingVertical: 4,
                  ...shadows.level1,
                }}>
                  <CaptionText style={{
                    color: colors.background.secondary,
                    fontSize: 10,
                    fontWeight: '600',
                  }}>PRO</CaptionText>
                </View>
              )}
            </View>

            {/* Upload Progress */}
            {isUploading && (
              <View style={{ marginTop: spacing.sm, width: '100%' }}>
                <View style={{
                  height: 4,
                  backgroundColor: colors.extended.lightest,
                  borderRadius: 2,
                  overflow: 'hidden',
                }}>
                  <Animated.View style={{
                    height: '100%',
                    backgroundColor: colors.accent, // Rich Gold progress bar
                    width: `${uploadProgress}%`,
                  }} />
                </View>
                <CaptionText style={{
                  textAlign: 'center',
                  marginTop: spacing.xs,
                  color: colors.text.tertiary
                }}>
                  Uploading... {Math.round(uploadProgress)}%
                </CaptionText>
              </View>
            )}
          </View>

          {/* Error Message */}
          {error && (
            <View style={{
              backgroundColor: colors.extended.lightest,
              borderLeftWidth: 4,
              borderLeftColor: colors.error,
              padding: spacing.sm,
              marginBottom: spacing.md,
              borderRadius: borderRadius.sm,
            }}>
              <CaptionText style={{ color: colors.error }}>
                {error}
              </CaptionText>
            </View>
          )}

          {/* Upload Options */}
          <View style={{ gap: spacing.sm }}>
            <PrimaryButton
              onPress={() => handleImagePicker(true)}
              disabled={isUploading}
              testID="take-photo-button"
            >
              📷 Take Photo
            </PrimaryButton>

            <SecondaryButton
              onPress={() => handleImagePicker(false)}
              disabled={isUploading}
              testID="choose-gallery-button"
            >
              🖼️ Choose from Gallery
            </SecondaryButton>

            {currentAvatar && (
              <TertiaryButton
                onPress={handleRemovePhoto}
                disabled={isUploading}
                testID="remove-photo-button"
                style={{
                  borderColor: colors.error,
                }}
              >
                <ButtonText style={{ color: colors.error }}>
                  🗑️ Remove Photo
                </ButtonText>
              </TertiaryButton>
            )}
          </View>
        </Animated.View>
      </Animated.View>
    </Modal>
  );
};

// Styles are now defined inline using theme

export default AvatarUpload;
