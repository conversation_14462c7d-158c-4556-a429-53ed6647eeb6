/**
 * Responsive Utilities
 * Provides responsive spacing and font size utilities following EPIC-01 approach
 */

import { Dimensions, Platform } from 'react-native';

// Get screen dimensions
const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Base dimensions for scaling (iPhone 8/SE size)
const BASE_WIDTH = 375;
const BASE_HEIGHT = 667;

/**
 * Get responsive spacing based on screen width
 * Scales spacing proportionally but caps at reasonable limits
 */
export const getResponsiveSpacing = (baseSpacing: number): number => {
  const scale = Math.min(SCREEN_WIDTH / BASE_WIDTH, 1.2); // Cap scaling at 1.2x
  return Math.round(baseSpacing * scale);
};

/**
 * Get responsive font size based on screen width
 * Ensures minimum and maximum font sizes for readability
 */
export const getResponsiveFontSize = (baseFontSize: number): number => {
  const scale = SCREEN_WIDTH / BASE_WIDTH;
  const newSize = baseFontSize * scale;

  // Ensure minimum and maximum font sizes
  if (newSize < 12) return 12;
  if (newSize > 32) return 32;

  return Math.round(newSize);
};

/**
 * Get responsive icon size
 * Smaller scaling factor for icons to maintain visual balance
 */
export const getResponsiveIconSize = (baseSize: number): number => {
  const scale = Math.min(SCREEN_WIDTH / BASE_WIDTH, 1.1); // Smaller scaling for icons
  return Math.round(baseSize * scale);
};

/**
 * Device type definitions
 */
export type DeviceType = 'small_phone' | 'phone' | 'large_phone' | 'tablet';

/**
 * Get the current device type based on screen width
 */
export const getDeviceType = (): DeviceType => {
  if (SCREEN_WIDTH < 320) {
    return 'small_phone';
  } else if (SCREEN_WIDTH < 375) {
    return 'phone';
  } else if (SCREEN_WIDTH < 414) {
    return 'large_phone';
  } else {
    return 'tablet';
  }
};

/**
 * Check if device is a tablet
 */
export const isTablet = (): boolean => {
  return SCREEN_WIDTH >= 768;
};

/**
 * Check if device is a small phone
 */
export const isSmallPhone = (): boolean => {
  return SCREEN_WIDTH < 375;
};

/**
 * Check if device is a large phone
 */
export const isLargePhone = (): boolean => {
  return SCREEN_WIDTH >= 414;
};

/**
 * Get platform-specific shadow styles
 */
export const getPlatformShadow = (elevation: number = 4) => {
  if (Platform.OS === 'ios') {
    return {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: elevation / 2 },
      shadowOpacity: 0.1,
      shadowRadius: elevation,
    };
  } else {
    return {
      elevation,
    };
  }
};

/**
 * Get minimum touch target size for accessibility
 */
export const getMinimumTouchTarget = (): number => {
  return Platform.OS === 'ios' ? 44 : 48;
};

/**
 * Get screen dimensions
 */
export const getScreenDimensions = () => ({
  width: SCREEN_WIDTH,
  height: SCREEN_HEIGHT,
  aspectRatio: SCREEN_WIDTH / SCREEN_HEIGHT,
});

/**
 * Responsive breakpoints
 */
export const Breakpoints = {
  SMALL_PHONE: 375,
  LARGE_PHONE: 414,
  TABLET: 768,
  LARGE_TABLET: 1024,
} as const;


