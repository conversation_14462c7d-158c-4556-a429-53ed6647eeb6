/**
 * ProfileForm Component
 *
 * Editable form for user profile information
 * Following the "digital sanctuary" design philosophy with modern form patterns
 */

import React, { useCallback, useEffect, useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
} from 'react-native';
import * as Haptics from 'expo-haptics';
import { User, UserProfile } from '../../services/api/profile';
import { PrimaryButton, SecondaryButton } from '../ui';
import { ModernInput } from '../ui/ModernInput';
import { useTheme } from '../../contexts/ThemeContext';
import { Headline2 } from '../ui/Typography';
import { profileAPI } from '../../services/api/profile';

interface ProfileFormProps {
  user: User;
  profile: UserProfile | null;
  formData: any;
  setFormData: (data: any) => void;
  errors: Record<string, string>;
  onSave: () => void;
  onCancel: () => void;
  isLoading?: boolean;
  enableRealTimeValidation?: boolean;
}

export const ProfileForm: React.FC<ProfileFormProps> = ({
  user,
  profile,
  formData,
  setFormData,
  errors,
  onSave,
  onCancel,
  isLoading = false,
  enableRealTimeValidation = true,
}) => {
  const { colors, borderRadius, shadows, spacing } = useTheme();
  const [realTimeErrors, setRealTimeErrors] = useState<Record<string, string>>({});
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Create styles using theme
  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    section: {
      backgroundColor: colors.background.secondary, // Warm Cream background
      marginBottom: spacing.md, // 16pt spacing
      borderRadius: borderRadius.lg, // 12pt corner radius as per design system
      padding: spacing.md, // 16pt internal padding
      ...shadows.level1, // Level 1 elevation shadow for cards
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.lg,
      gap: spacing.md,
    },
    button: {
      flex: 1,
    },
  });

  // Real-time validation function
  const validateField = useCallback((field: string, value: any) => {
    if (!enableRealTimeValidation) return null;

    const validationData = { [field]: value };
    const result = profileAPI.validateProfileData(validationData);

    if (!result.isValid && result.errors.length > 0) {
      return result.errors[0];
    }

    // Additional field-specific validation
    switch (field) {
      case 'first_name':
      case 'last_name':
        return !value?.trim() ? `${field.replace('_', ' ')} is required` : null;
      case 'email':
        return !value?.trim() ? 'Email is required' :
               !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value) ? 'Invalid email format' : null;
      case 'website':
        return value && !/^https?:\/\/.+/.test(value) ? 'Website must start with http:// or https://' : null;
      case 'years_of_experience':
        return value && (value < 0 || value > 50) ? 'Years of experience must be between 0 and 50' : null;
      default:
        return null;
    }
  }, [enableRealTimeValidation]);

  const updateFormData = useCallback((field: string, value: string | number) => {
    setFormData((prev: any) => ({
      ...prev,
      [field]: value,
    }));

    setHasUnsavedChanges(true);

    // Real-time validation
    if (enableRealTimeValidation) {
      const error = validateField(field, value);
      setRealTimeErrors(prev => ({
        ...prev,
        [field]: error || '',
      }));
    }
  }, [validateField, enableRealTimeValidation, setFormData]);

  const handleSave = useCallback(() => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    setHasUnsavedChanges(false);
    onSave();
  }, [onSave]);

  const handleCancel = useCallback(() => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setHasUnsavedChanges(false);
    setRealTimeErrors({});
    onCancel();
  }, [onCancel]);

  // Check if form is valid for save button state
  const isFormValid = useCallback(() => {
    const hasErrors = Object.values(errors).some(error => error) ||
                     Object.values(realTimeErrors).some(error => error);
    const hasRequiredFields = formData.first_name?.trim() && formData.last_name?.trim();
    return !hasErrors && hasRequiredFields;
  }, [errors, realTimeErrors, formData.first_name, formData.last_name]);

  const FormSection: React.FC<{
    title: string;
    children: React.ReactNode;
  }> = ({ title, children }) => (
    <View style={styles.section}>
      <Headline2 color="secondary" style={{ marginBottom: spacing.md }}>
        {title}
      </Headline2>
      {children}
    </View>
  );

  return (
    <View style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Personal Information */}
        <FormSection title="Personal Information">
          <ModernInput
            label="First Name"
            value={formData.first_name || user?.first_name || ''}
            onChangeText={(value) => updateFormData('first_name', value)}
            error={errors.first_name || realTimeErrors.first_name}
            testID="first-name-input"
            required
          />

          <ModernInput
            label="Last Name"
            value={formData.last_name || user?.last_name || ''}
            onChangeText={(value) => updateFormData('last_name', value)}
            error={errors.last_name || realTimeErrors.last_name}
            testID="last-name-input"
            required
          />

          <ModernInput
            label="Email Address"
            value={formData.email || user?.email || ''}
            onChangeText={(value) => updateFormData('email', value)}
            error={errors.email || realTimeErrors.email}
            keyboardType="email-address"
            autoCapitalize="none"
            testID="email-input"
            required
          />

          <ModernInput
            label="Phone Number"
            value={formData.phone || user?.phone || ''}
            onChangeText={(value) => updateFormData('phone', value)}
            error={errors.phone || realTimeErrors.phone}
            keyboardType="phone-pad"
            testID="phone-input"
            helperText="Include country code (e.g., +1234567890)"
          />

          <ModernInput
            label="Date of Birth"
            value={formData.date_of_birth || user?.date_of_birth || ''}
            onChangeText={(value) => updateFormData('date_of_birth', value)}
            error={errors.date_of_birth || realTimeErrors.date_of_birth}
            helperText="Format: YYYY-MM-DD"
            testID="date-of-birth-input"
          />

          <ModernInput
            label="Bio"
            value={formData.bio || user?.bio || ''}
            onChangeText={(value) => updateFormData('bio', value)}
            error={errors.bio || realTimeErrors.bio}
            helperText="Tell us about yourself (max 500 characters)"
            multiline
            numberOfLines={3}
            testID="bio-input"
            maxLength={500}
          />
        </FormSection>

        {/* Address Information */}
        <FormSection title="Location">
          <ModernInput
            label="Street Address"
            value={formData.address || profile?.address || ''}
            onChangeText={(value) => updateFormData('address', value)}
            error={errors.address}
            testID="address-input"
          />

          <ModernInput
            label="City"
            value={formData.city || profile?.city || ''}
            onChangeText={(value) => updateFormData('city', value)}
            error={errors.city}
            testID="city-input"
          />

          <ModernInput
            label="State/Province"
            value={formData.state || profile?.state || ''}
            onChangeText={(value) => updateFormData('state', value)}
            error={errors.state}
            testID="state-input"
          />

          <ModernInput
            label="Postal Code"
            value={formData.postal_code || profile?.postal_code || ''}
            onChangeText={(value) => updateFormData('postal_code', value)}
            error={errors.postal_code}
            testID="postal-code-input"
          />

          <ModernInput
            label="Country"
            value={formData.country || profile?.country || ''}
            onChangeText={(value) => updateFormData('country', value)}
            error={errors.country}
            testID="country-input"
          />
        </FormSection>

        {/* Professional Information (for providers) */}
        {user?.role === 'service_provider' && (
          <FormSection title="Business Information">
            <ModernInput
              label="Business Name"
              value={formData.business_name || profile?.business_name || ''}
              onChangeText={(value) => updateFormData('business_name', value)}
              error={errors.business_name}
              testID="business-name-input"
              required
            />

            <ModernInput
              label="Business Description"
              value={formData.business_description || profile?.business_description || ''}
              onChangeText={(value) => updateFormData('business_description', value)}
              error={errors.business_description}
              helperText="Describe the services you provide"
              multiline
              numberOfLines={3}
              testID="business-description-input"
            />

            <ModernInput
              label="Years of Experience"
              value={formData.years_of_experience?.toString() || profile?.years_of_experience?.toString() || ''}
              onChangeText={(value) => updateFormData('years_of_experience', parseInt(value) || 0)}
              error={errors.years_of_experience}
              keyboardType="numeric"
              testID="years-experience-input"
            />

            <ModernInput
              label="Website"
              value={formData.website || profile?.website || ''}
              onChangeText={(value) => updateFormData('website', value)}
              error={errors.website}
              helperText="Your business website URL"
              keyboardType="url"
              autoCapitalize="none"
              testID="website-input"
            />

            <ModernInput
              label="Instagram"
              value={formData.instagram || profile?.instagram || ''}
              onChangeText={(value) => updateFormData('instagram', value)}
              error={errors.instagram}
              helperText="Your Instagram handle (without @)"
              autoCapitalize="none"
              testID="instagram-input"
            />

            <ModernInput
              label="Facebook"
              value={formData.facebook || profile?.facebook || ''}
              onChangeText={(value) => updateFormData('facebook', value)}
              error={errors.facebook}
              helperText="Your Facebook page name"
              autoCapitalize="none"
              testID="facebook-input"
            />
          </FormSection>
        )}

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <SecondaryButton
            onPress={handleCancel}
            disabled={isLoading}
            testID="cancel-profile-button"
            style={styles.button}
          >
            Cancel
          </SecondaryButton>

          <PrimaryButton
            onPress={handleSave}
            disabled={isLoading || !isFormValid()}
            loading={isLoading}
            testID="save-profile-button"
            style={styles.button}
          >
            {isLoading ? "Saving..." : hasUnsavedChanges ? "Save Changes" : "No Changes"}
          </PrimaryButton>
        </View>
      </ScrollView>
    </View>
  );
};

// Styles are now defined inline using theme

export default ProfileForm;
