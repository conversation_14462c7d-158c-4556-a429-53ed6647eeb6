/**
 * ProfileScreen Test Suite - Working Implementation
 * Tests for the main ProfileScreen component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react-native';
import { Alert } from 'react-native';

// Mock navigation
const mockNavigation = {
  navigate: jest.fn(),
  goBack: jest.fn(),
  setOptions: jest.fn(),
};

const mockRoute = {
  params: {},
};

// Mock dependencies
jest.mock('../../../components/ui/SafeAreaViewWrapper', () => ({
  SafeAreaViewWrapper: ({ children, testID }: any) => <div testID={testID}>{children}</div>,
}));

jest.mock('../../../components/ui/AnimatedCard', () => ({
  AnimatedCard: ({ children, testID }: any) => <div testID={testID}>{children}</div>,
}));

jest.mock('../../../components/ui/FadeTransition', () => ({
  FadeTransition: ({ children, visible, testID }: any) => 
    visible ? <div testID={testID}>{children}</div> : null,
}));

jest.mock('../../../components/ui/LoadingAnimation', () => ({
  LoadingAnimation: ({ testID }: any) => <div testID={testID}>Loading...</div>,
}));

// Mock profile components
jest.mock('../../../components/profile', () => ({
  ProfileDisplay: ({ user, profile }: any) => (
    <div testID="profile-display">
      <div>{user?.full_name}</div>
      <div>{user?.email}</div>
    </div>
  ),
  ProfileForm: ({ user, onSave, onCancel }: any) => (
    <div testID="profile-form">
      <button testID="save-button" onPress={onSave}>Save</button>
      <button testID="cancel-button" onPress={onCancel}>Cancel</button>
    </div>
  ),
  AvatarUpload: ({ isVisible, onClose }: any) => 
    isVisible ? (
      <div testID="avatar-upload">
        <button testID="close-avatar" onPress={onClose}>Close</button>
      </div>
    ) : null,
}));

// Mock profile API
const mockProfileAPI = {
  getProfile: jest.fn(),
  getProfileDetails: jest.fn(),
  updateProfile: jest.fn(),
  updateProfileDetails: jest.fn(),
  uploadAvatar: jest.fn(),
  deleteAvatar: jest.fn(),
};

jest.mock('../../../services/api/profile', () => ({
  profileAPI: mockProfileAPI,
}));

// Mock React Native components
jest.mock('react-native', () => ({
  ...jest.requireActual('react-native'),
  Alert: {
    alert: jest.fn(),
  },
  Dimensions: {
    get: () => ({ width: 375, height: 812 }),
  },
}));

// Import component after mocks
import { ProfileScreen } from '../ProfileScreen';

// Mock data
const mockUser = {
  id: '1',
  email: '<EMAIL>',
  username: 'testuser',
  first_name: 'John',
  last_name: 'Doe',
  full_name: 'John Doe',
  phone: '+**********',
  role: 'customer' as const,
  avatar: 'https://example.com/avatar.jpg',
  date_of_birth: '1990-01-01',
  bio: 'Test bio',
  account_status: 'active' as const,
  is_verified: true,
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z',
};

const mockProfile = {
  address: '123 Main St',
  city: 'Toronto',
  state: 'ON',
  zip_code: 'M5V 3A8',
  country: 'Canada',
  latitude: 43.6532,
  longitude: -79.3832,
  business_name: 'Test Business',
  business_description: 'Professional services',
  years_of_experience: 5,
  website: 'https://testbusiness.com',
  instagram: '@testbusiness',
  facebook: 'testbusiness',
  search_radius: 25,
  auto_accept_bookings: false,
  show_phone_publicly: true,
  show_email_publicly: false,
  allow_reviews: true,
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z',
};

describe('ProfileScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockProfileAPI.getProfile.mockResolvedValue(mockUser);
    mockProfileAPI.getProfileDetails.mockResolvedValue(mockProfile);
  });

  it('should render loading state initially', () => {
    render(<ProfileScreen navigation={mockNavigation as any} route={mockRoute as any} />);
    
    expect(screen.getByTestId('loading-animation')).toBeTruthy();
  });

  it('should load and display profile data', async () => {
    render(<ProfileScreen navigation={mockNavigation as any} route={mockRoute as any} />);

    await waitFor(() => {
      expect(mockProfileAPI.getProfile).toHaveBeenCalled();
      expect(mockProfileAPI.getProfileDetails).toHaveBeenCalled();
    });

    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeTruthy();
      expect(screen.getByText('<EMAIL>')).toBeTruthy();
    });
  });

  it('should handle API errors gracefully', async () => {
    const consoleError = jest.spyOn(console, 'error').mockImplementation();
    mockProfileAPI.getProfile.mockRejectedValue(new Error('API Error'));

    render(<ProfileScreen navigation={mockNavigation as any} route={mockRoute as any} />);

    await waitFor(() => {
      expect(consoleError).toHaveBeenCalled();
    });

    consoleError.mockRestore();
  });

  it('should toggle edit mode', async () => {
    render(<ProfileScreen navigation={mockNavigation as any} route={mockRoute as any} />);

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeTruthy();
    });

    // Find and press edit button
    const editButton = screen.getByText('Edit');
    fireEvent.press(editButton);

    // Should show form
    await waitFor(() => {
      expect(screen.getByTestId('profile-form')).toBeTruthy();
    });
  });

  it('should handle profile save', async () => {
    mockProfileAPI.updateProfile.mockResolvedValue(mockUser);

    render(<ProfileScreen navigation={mockNavigation as any} route={mockRoute as any} />);

    // Wait for data to load and enter edit mode
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeTruthy();
    });

    const editButton = screen.getByText('Edit');
    fireEvent.press(editButton);

    await waitFor(() => {
      expect(screen.getByTestId('profile-form')).toBeTruthy();
    });

    // Press save button
    const saveButton = screen.getByTestId('save-button');
    fireEvent.press(saveButton);

    await waitFor(() => {
      expect(mockProfileAPI.updateProfile).toHaveBeenCalled();
    });
  });

  it('should handle profile cancel', async () => {
    render(<ProfileScreen navigation={mockNavigation as any} route={mockRoute as any} />);

    // Wait for data to load and enter edit mode
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeTruthy();
    });

    const editButton = screen.getByText('Edit');
    fireEvent.press(editButton);

    await waitFor(() => {
      expect(screen.getByTestId('profile-form')).toBeTruthy();
    });

    // Press cancel button
    const cancelButton = screen.getByTestId('cancel-button');
    fireEvent.press(cancelButton);

    // Should return to display mode
    await waitFor(() => {
      expect(screen.getByTestId('profile-display')).toBeTruthy();
    });
  });

  it('should handle avatar upload', async () => {
    render(<ProfileScreen navigation={mockNavigation as any} route={mockRoute as any} />);

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeTruthy();
    });

    // Find and press avatar
    const avatar = screen.getByTestId('user-avatar');
    fireEvent.press(avatar);

    // Should show avatar upload modal
    await waitFor(() => {
      expect(screen.getByTestId('avatar-upload')).toBeTruthy();
    });

    // Close modal
    const closeButton = screen.getByTestId('close-avatar');
    fireEvent.press(closeButton);

    // Modal should be hidden
    await waitFor(() => {
      expect(screen.queryByTestId('avatar-upload')).toBeNull();
    });
  });

  it('should handle refresh', async () => {
    render(<ProfileScreen navigation={mockNavigation as any} route={mockRoute as any} />);

    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeTruthy();
    });

    // Clear previous calls
    jest.clearAllMocks();
    mockProfileAPI.getProfile.mockResolvedValue(mockUser);
    mockProfileAPI.getProfileDetails.mockResolvedValue(mockProfile);

    // Trigger refresh
    const scrollView = screen.getByTestId('profile-scroll-view');
    fireEvent(scrollView, 'refresh');

    await waitFor(() => {
      expect(mockProfileAPI.getProfile).toHaveBeenCalled();
      expect(mockProfileAPI.getProfileDetails).toHaveBeenCalled();
    });
  });

  it('should handle settings navigation', async () => {
    render(<ProfileScreen navigation={mockNavigation as any} route={mockRoute as any} />);

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeTruthy();
    });

    // Find and press settings button
    const settingsButton = screen.getByText('⚙️');
    fireEvent.press(settingsButton);

    expect(mockNavigation.navigate).toHaveBeenCalledWith('Settings');
  });
});
