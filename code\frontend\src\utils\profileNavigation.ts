/**
 * Profile Navigation Utilities
 * Helper functions for profile-related navigation and deep linking
 */

import React from 'react';
import { NavigationProp } from '@react-navigation/native';
import { ProfileStackParamList } from '../navigation/MainNavigator';

export type ProfileNavigationProp = NavigationProp<ProfileStackParamList>;

export interface ProfileDeepLinkParams {
  section?: 'edit' | 'avatar' | 'settings';
  tab?: string;
  action?: string;
}

/**
 * Navigate to profile with specific section
 */
export const navigateToProfile = (
  navigation: ProfileNavigationProp,
  params?: ProfileDeepLinkParams
) => {
  navigation.navigate('Profile', params);
};

/**
 * Navigate to settings
 */
export const navigateToSettings = (navigation: ProfileNavigationProp) => {
  navigation.navigate('Settings');
};

/**
 * Generate deep link URL for profile sections
 */
export const generateProfileDeepLink = (params?: ProfileDeepLinkParams): string => {
  const baseUrl = 'vierla://profile';
  
  if (!params) return baseUrl;
  
  const queryParams = new URLSearchParams();
  
  if (params.section) queryParams.append('section', params.section);
  if (params.tab) queryParams.append('tab', params.tab);
  if (params.action) queryParams.append('action', params.action);
  
  const queryString = queryParams.toString();
  return queryString ? `${baseUrl}?${queryString}` : baseUrl;
};

/**
 * Parse profile deep link parameters
 */
export const parseProfileDeepLink = (url: string): ProfileDeepLinkParams | null => {
  try {
    const urlObj = new URL(url);
    const params: ProfileDeepLinkParams = {};
    
    const section = urlObj.searchParams.get('section');
    if (section && ['edit', 'avatar', 'settings'].includes(section)) {
      params.section = section as 'edit' | 'avatar' | 'settings';
    }
    
    const tab = urlObj.searchParams.get('tab');
    if (tab) params.tab = tab;
    
    const action = urlObj.searchParams.get('action');
    if (action) params.action = action;
    
    return Object.keys(params).length > 0 ? params : null;
  } catch (error) {
    console.error('Error parsing profile deep link:', error);
    return null;
  }
};

/**
 * Profile navigation state management
 */
export class ProfileNavigationManager {
  private static instance: ProfileNavigationManager;
  private navigationRef: ProfileNavigationProp | null = null;
  private pendingNavigation: ProfileDeepLinkParams | null = null;

  static getInstance(): ProfileNavigationManager {
    if (!ProfileNavigationManager.instance) {
      ProfileNavigationManager.instance = new ProfileNavigationManager();
    }
    return ProfileNavigationManager.instance;
  }

  setNavigationRef(ref: ProfileNavigationProp) {
    this.navigationRef = ref;
    
    // Execute any pending navigation
    if (this.pendingNavigation) {
      this.navigateToSection(this.pendingNavigation);
      this.pendingNavigation = null;
    }
  }

  navigateToSection(params: ProfileDeepLinkParams) {
    if (this.navigationRef) {
      navigateToProfile(this.navigationRef, params);
    } else {
      // Store for later execution
      this.pendingNavigation = params;
    }
  }

  navigateToSettings() {
    if (this.navigationRef) {
      navigateToSettings(this.navigationRef);
    }
  }
}

/**
 * Hook for profile navigation
 */
export const useProfileNavigation = (navigation: ProfileNavigationProp) => {
  const manager = ProfileNavigationManager.getInstance();
  
  // Register navigation reference
  React.useEffect(() => {
    manager.setNavigationRef(navigation);
  }, [navigation, manager]);

  return {
    navigateToProfile: (params?: ProfileDeepLinkParams) => 
      navigateToProfile(navigation, params),
    navigateToSettings: () => navigateToSettings(navigation),
    generateDeepLink: generateProfileDeepLink,
    parseDeepLink: parseProfileDeepLink,
  };
};
